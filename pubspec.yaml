name: expense_tracker
description: "Offline-first expense tracker with Firebase sync"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.7.2

dependencies:
  flutter:
    sdk: flutter

  # UI & Icons
  cupertino_icons: ^1.0.8
  material_symbols_icons: ^4.2785.1

  # State Management
  flutter_riverpod: ^2.5.1
  riverpod_annotation: ^2.3.5

  # Data Models
  freezed_annotation: ^2.4.4
  json_annotation: ^4.9.0

  # Database
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  path: ^1.9.0

  # Firebase
  firebase_core: ^3.8.0
  firebase_auth: ^5.3.3
  cloud_firestore: ^5.5.0

  # Connectivity & Network
  connectivity_plus: ^6.1.0
  internet_connection_checker: ^3.0.1
  shared_preferences: ^2.3.3

  # Utilities
  uuid: ^4.5.1
  intl: ^0.20.2

  # Platform specific
  flutter_secure_storage: ^9.2.2

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Linting
  flutter_lints: ^5.0.0

  # Code Generation
  build_runner: ^2.4.13
  riverpod_generator: ^2.4.3
  riverpod_lint: ^2.3.12
  freezed: ^2.5.7
  json_serializable: ^6.8.0
  hive_generator: ^2.0.1

# Removed dependency_overrides - these were causing the conflicts

flutter:
  uses-material-design: true