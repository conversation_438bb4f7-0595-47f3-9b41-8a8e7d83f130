# Expense Tracker

An offline-first expense tracker built with Flutter and Firebase, featuring automatic synchronization when online.

## Features

- ✅ **Offline-First**: Full functionality without internet connection
- ✅ **Firebase Sync**: Automatic data synchronization when online
- ✅ **Account Management**: Multiple accounts (Cash, Bank) with balance tracking
- ✅ **Transaction Tracking**: Income and expense management with rich details
- ✅ **Smart UI**: Intuitive interface with Material Design 3
- ✅ **PKR Currency**: Pakistani Rupee support
- ✅ **Real-time Sync**: Automatic conflict resolution and data consistency

## Architecture

- **Clean Architecture**: Separation of concerns with data, domain, and presentation layers
- **State Management**: Riverpod for reactive state management
- **Local Storage**: Hive for offline data persistence
- **Cloud Storage**: Firebase Firestore for data synchronization
- **Authentication**: Firebase Anonymous Authentication

## Firebase Setup

### 1. Enable Anonymous Authentication

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project: `expense-tracker-b3f2b`
3. Navigate to **Authentication** → **Sign-in method**
4. Enable **Anonymous** authentication
5. Click **Save**

### 2. Deploy Firestore Security Rules

```bash
# Install Firebase CLI if not already installed
npm install -g firebase-tools

# Login to Firebase
firebase login

# Initialize Firebase in your project (if not done)
firebase init firestore

# Deploy security rules
firebase deploy --only firestore:rules

# Deploy indexes
firebase deploy --only firestore:indexes
```

### 3. Firestore Security Rules

The project includes security rules in `firestore.rules` that:
- Allow authenticated users to access only their own data
- Secure user accounts and transactions collections
- Deny all unauthorized access

### 4. Required Firestore Indexes

The project includes optimized indexes in `firestore.indexes.json` for:
- Account queries sorted by update time
- Transaction queries sorted by date
- Account-specific transaction queries

## Getting Started

### Prerequisites

- Flutter SDK (^3.7.2)
- FVM (Flutter Version Management)
- Firebase CLI
- iOS Simulator or Android Emulator

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd expense_tracker
   ```

2. **Install Flutter dependencies**
   ```bash
   fvm flutter pub get
   ```

3. **Generate code**
   ```bash
   fvm flutter packages pub run build_runner build
   ```

4. **Run the app**
   ```bash
   fvm flutter run
   ```

## Project Structure

```
lib/
├── core/                 # Core utilities and constants
│   ├── constants/        # App constants
│   ├── theme/           # App theming
│   └── utils/           # Extensions and utilities
├── data/                # Data layer
│   ├── database/        # Local database (Hive)
│   ├── models/          # Data models
│   └── repositories/    # Data repositories
├── presentation/        # UI layer
│   ├── screens/         # App screens
│   └── widgets/         # Reusable widgets
├── providers/           # Riverpod providers
├── services/            # Business logic services
└── main.dart           # App entry point
```

## Key Technologies

- **Flutter**: Cross-platform mobile framework
- **Riverpod**: State management and dependency injection
- **Hive**: Local NoSQL database
- **Firebase**: Backend-as-a-Service
- **Freezed**: Immutable data classes
- **Material Design 3**: Modern UI design system

## Troubleshooting

### Firebase Authentication Error

If you see `[firebase_auth/admin-restricted-operation]`:
1. Enable Anonymous Authentication in Firebase Console
2. Deploy the security rules using Firebase CLI

### UI Layout Errors

The project has been optimized to handle various screen sizes and orientations. If you encounter layout issues, ensure you're using the latest version.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
