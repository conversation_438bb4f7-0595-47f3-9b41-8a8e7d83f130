import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../data/models/transfer.dart';
import '../data/repositories/transfer_repository.dart';
import 'account_providers.dart';

part 'transfer_providers.g.dart';

/// Transfer repository provider
@riverpod
TransferRepository transferRepository(Ref ref) {
  return TransferRepository();
}

/// Transfers provider - provides all transfers
@riverpod
class Transfers extends _$Transfers {
  @override
  Future<List<Transfer>> build() async {
    final repository = ref.read(transferRepositoryProvider);
    await repository.initialize();
    return repository.getAllTransfers();
  }

  /// Create a new transfer
  Future<Transfer> createTransfer({
    required String fromAccountId,
    required String toAccountId,
    required double amount,
    String? description,
    DateTime? date,
  }) async {
    final transferRepository = ref.read(transferRepositoryProvider);

    // Validate accounts exist
    final fromAccount = await ref.read(accountByIdProvider(fromAccountId).future);
    final toAccount = await ref.read(accountByIdProvider(toAccountId).future);

    if (fromAccount == null) {
      throw Exception('Source account not found');
    }
    if (toAccount == null) {
      throw Exception('Destination account not found');
    }

    // Check sufficient balance
    final fromAccountBalance = await ref.read(accountBalanceProvider(fromAccountId).future);
    if (fromAccountBalance < amount) {
      throw Exception('Insufficient funds in source account');
    }

    // Create the transfer
    final transfer = TransferHelper.createTransfer(
      fromAccountId: fromAccountId,
      toAccountId: toAccountId,
      amount: amount,
      description: description,
      date: date,
    );

    // Save the transfer
    final createdTransfer = await transferRepository.createTransfer(transfer);

    // Update account balances by invalidating the account balance providers
    ref.invalidate(accountBalanceProvider(fromAccountId));
    ref.invalidate(accountBalanceProvider(toAccountId));
    ref.invalidate(accountBalanceNotifierProvider);
    ref.invalidate(totalBalanceNotifierProvider);

    // Refresh the transfers list
    ref.invalidateSelf();

    return createdTransfer;
  }

  /// Update an existing transfer
  Future<Transfer> updateTransfer(Transfer transfer) async {
    final repository = ref.read(transferRepositoryProvider);

    final updatedTransfer = await repository.updateTransfer(transfer);

    // Invalidate related providers
    ref.invalidate(accountBalanceProvider(transfer.fromAccountId));
    ref.invalidate(accountBalanceProvider(transfer.toAccountId));
    ref.invalidate(accountBalanceNotifierProvider);
    ref.invalidate(totalBalanceNotifierProvider);

    // Refresh the transfers list
    ref.invalidateSelf();

    return updatedTransfer;
  }

  /// Delete a transfer
  Future<void> deleteTransfer(String id) async {
    final repository = ref.read(transferRepositoryProvider);

    // Get the transfer before deleting to know which accounts to refresh
    final transfer = await repository.getTransferById(id);
    if (transfer == null) {
      throw Exception('Transfer not found');
    }

    await repository.deleteTransfer(id);

    // Invalidate related providers
    ref.invalidate(accountBalanceProvider(transfer.fromAccountId));
    ref.invalidate(accountBalanceProvider(transfer.toAccountId));
    ref.invalidate(accountBalanceNotifierProvider);
    ref.invalidate(totalBalanceNotifierProvider);

    // Refresh the transfers list
    ref.invalidateSelf();
  }

  /// Refresh transfers
  Future<void> refresh() async {
    ref.invalidateSelf();
  }
}

/// Transfer by ID provider
@riverpod
Future<Transfer?> transferById(Ref ref, String id) async {
  final repository = ref.read(transferRepositoryProvider);
  await repository.initialize();
  return repository.getTransferById(id);
}

/// Transfers for account provider
@riverpod
Future<List<Transfer>> transfersForAccount(
  Ref ref,
  String accountId,
) async {
  final repository = ref.read(transferRepositoryProvider);
  await repository.initialize();
  return repository.getTransfersForAccount(accountId);
}

/// Recent transfers provider
@riverpod
Future<List<Transfer>> recentTransfers(
  Ref ref, {
  int days = 30,
}) async {
  final repository = ref.read(transferRepositoryProvider);
  await repository.initialize();
  return repository.getRecentTransfers(days: days);
}

/// Transfer statistics for account provider
@riverpod
Future<Map<String, dynamic>> transferStatsForAccount(
  Ref ref,
  String accountId,
) async {
  final repository = ref.read(transferRepositoryProvider);
  await repository.initialize();
  return repository.getTransferStatsForAccount(accountId);
}

/// Transfer search provider
@riverpod
Future<List<Transfer>> transferSearch(
  Ref ref,
  String query,
) async {
  final repository = ref.read(transferRepositoryProvider);
  await repository.initialize();
  return repository.searchTransfers(query);
}

/// Transfer validation provider
@riverpod
class TransferValidation extends _$TransferValidation {
  @override
  String? build() {
    return null; // No validation error initially
  }

  /// Validate a transfer
  Future<bool> validateTransfer({
    required String fromAccountId,
    required String toAccountId,
    required double amount,
  }) async {
    try {
      // Check if accounts are different
      if (fromAccountId == toAccountId) {
        state = 'Source and destination accounts must be different';
        return false;
      }

      // Check if amount is positive
      if (amount <= 0) {
        state = 'Transfer amount must be greater than zero';
        return false;
      }

      // Check if accounts exist
      final fromAccount = await ref.read(accountByIdProvider(fromAccountId).future);
      final toAccount = await ref.read(accountByIdProvider(toAccountId).future);

      if (fromAccount == null) {
        state = 'Source account not found';
        return false;
      }

      if (toAccount == null) {
        state = 'Destination account not found';
        return false;
      }

      // Check sufficient balance
      final fromAccountBalance = await ref.read(accountBalanceProvider(fromAccountId).future);
      if (fromAccountBalance < amount) {
        state = 'Insufficient funds in source account';
        return false;
      }

      // All validations passed
      state = null;
      return true;
    } catch (e) {
      state = 'Validation error: ${e.toString()}';
      return false;
    }
  }

  /// Clear validation error
  void clearError() {
    state = null;
  }
}

/// Monthly transfer amount provider
@riverpod
Future<double> monthlyTransferAmount(
  Ref ref,
  DateTime month,
  String accountId,
) async {
  final repository = ref.read(transferRepositoryProvider);
  await repository.initialize();
  return repository.getMonthlyTransferAmount(month, accountId);
}
