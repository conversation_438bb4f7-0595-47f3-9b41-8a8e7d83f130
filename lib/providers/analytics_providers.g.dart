// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'analytics_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$weeklySpendingTrendHash() =>
    r'2cada7f5c9a79026efa7a1486799cc6a3ca52d44';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Weekly spending trend
///
/// Copied from [weeklySpendingTrend].
@ProviderFor(weeklySpendingTrend)
const weeklySpendingTrendProvider = WeeklySpendingTrendFamily();

/// Weekly spending trend
///
/// Copied from [weeklySpendingTrend].
class WeeklySpendingTrendFamily
    extends Family<AsyncValue<List<Map<String, dynamic>>>> {
  /// Weekly spending trend
  ///
  /// Copied from [weeklySpendingTrend].
  const WeeklySpendingTrendFamily();

  /// Weekly spending trend
  ///
  /// Copied from [weeklySpendingTrend].
  WeeklySpendingTrendProvider call(
    DateTime startDate,
  ) {
    return WeeklySpendingTrendProvider(
      startDate,
    );
  }

  @override
  WeeklySpendingTrendProvider getProviderOverride(
    covariant WeeklySpendingTrendProvider provider,
  ) {
    return call(
      provider.startDate,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'weeklySpendingTrendProvider';
}

/// Weekly spending trend
///
/// Copied from [weeklySpendingTrend].
class WeeklySpendingTrendProvider
    extends AutoDisposeFutureProvider<List<Map<String, dynamic>>> {
  /// Weekly spending trend
  ///
  /// Copied from [weeklySpendingTrend].
  WeeklySpendingTrendProvider(
    DateTime startDate,
  ) : this._internal(
          (ref) => weeklySpendingTrend(
            ref as WeeklySpendingTrendRef,
            startDate,
          ),
          from: weeklySpendingTrendProvider,
          name: r'weeklySpendingTrendProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$weeklySpendingTrendHash,
          dependencies: WeeklySpendingTrendFamily._dependencies,
          allTransitiveDependencies:
              WeeklySpendingTrendFamily._allTransitiveDependencies,
          startDate: startDate,
        );

  WeeklySpendingTrendProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.startDate,
  }) : super.internal();

  final DateTime startDate;

  @override
  Override overrideWith(
    FutureOr<List<Map<String, dynamic>>> Function(
            WeeklySpendingTrendRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: WeeklySpendingTrendProvider._internal(
        (ref) => create(ref as WeeklySpendingTrendRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        startDate: startDate,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<Map<String, dynamic>>> createElement() {
    return _WeeklySpendingTrendProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is WeeklySpendingTrendProvider && other.startDate == startDate;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, startDate.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin WeeklySpendingTrendRef
    on AutoDisposeFutureProviderRef<List<Map<String, dynamic>>> {
  /// The parameter `startDate` of this provider.
  DateTime get startDate;
}

class _WeeklySpendingTrendProviderElement
    extends AutoDisposeFutureProviderElement<List<Map<String, dynamic>>>
    with WeeklySpendingTrendRef {
  _WeeklySpendingTrendProviderElement(super.provider);

  @override
  DateTime get startDate => (origin as WeeklySpendingTrendProvider).startDate;
}

String _$accountBalanceDistributionHash() =>
    r'54b092dff23219da8cba722f4acab2a29bd28a39';

/// Account balance distribution
///
/// Copied from [accountBalanceDistribution].
@ProviderFor(accountBalanceDistribution)
final accountBalanceDistributionProvider =
    AutoDisposeFutureProvider<List<Map<String, dynamic>>>.internal(
  accountBalanceDistribution,
  name: r'accountBalanceDistributionProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$accountBalanceDistributionHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AccountBalanceDistributionRef
    = AutoDisposeFutureProviderRef<List<Map<String, dynamic>>>;
String _$spendingInsightsHash() => r'4e58abe1674d7ee0aa93bf91b224d342d8b35b5f';

/// Spending insights and patterns
///
/// Copied from [spendingInsights].
@ProviderFor(spendingInsights)
final spendingInsightsProvider =
    AutoDisposeFutureProvider<Map<String, dynamic>>.internal(
  spendingInsights,
  name: r'spendingInsightsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$spendingInsightsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SpendingInsightsRef
    = AutoDisposeFutureProviderRef<Map<String, dynamic>>;
String _$categorySpendingChartDataHash() =>
    r'42eb1bedda129b744ba53ccc60ccb8a2a6918d93';

/// Chart data for spending by category (pie chart)
///
/// Copied from [categorySpendingChartData].
@ProviderFor(categorySpendingChartData)
const categorySpendingChartDataProvider = CategorySpendingChartDataFamily();

/// Chart data for spending by category (pie chart)
///
/// Copied from [categorySpendingChartData].
class CategorySpendingChartDataFamily
    extends Family<AsyncValue<List<Map<String, dynamic>>>> {
  /// Chart data for spending by category (pie chart)
  ///
  /// Copied from [categorySpendingChartData].
  const CategorySpendingChartDataFamily();

  /// Chart data for spending by category (pie chart)
  ///
  /// Copied from [categorySpendingChartData].
  CategorySpendingChartDataProvider call(
    DateTime month,
  ) {
    return CategorySpendingChartDataProvider(
      month,
    );
  }

  @override
  CategorySpendingChartDataProvider getProviderOverride(
    covariant CategorySpendingChartDataProvider provider,
  ) {
    return call(
      provider.month,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'categorySpendingChartDataProvider';
}

/// Chart data for spending by category (pie chart)
///
/// Copied from [categorySpendingChartData].
class CategorySpendingChartDataProvider
    extends AutoDisposeFutureProvider<List<Map<String, dynamic>>> {
  /// Chart data for spending by category (pie chart)
  ///
  /// Copied from [categorySpendingChartData].
  CategorySpendingChartDataProvider(
    DateTime month,
  ) : this._internal(
          (ref) => categorySpendingChartData(
            ref as CategorySpendingChartDataRef,
            month,
          ),
          from: categorySpendingChartDataProvider,
          name: r'categorySpendingChartDataProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$categorySpendingChartDataHash,
          dependencies: CategorySpendingChartDataFamily._dependencies,
          allTransitiveDependencies:
              CategorySpendingChartDataFamily._allTransitiveDependencies,
          month: month,
        );

  CategorySpendingChartDataProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.month,
  }) : super.internal();

  final DateTime month;

  @override
  Override overrideWith(
    FutureOr<List<Map<String, dynamic>>> Function(
            CategorySpendingChartDataRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: CategorySpendingChartDataProvider._internal(
        (ref) => create(ref as CategorySpendingChartDataRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        month: month,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<Map<String, dynamic>>> createElement() {
    return _CategorySpendingChartDataProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CategorySpendingChartDataProvider && other.month == month;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, month.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin CategorySpendingChartDataRef
    on AutoDisposeFutureProviderRef<List<Map<String, dynamic>>> {
  /// The parameter `month` of this provider.
  DateTime get month;
}

class _CategorySpendingChartDataProviderElement
    extends AutoDisposeFutureProviderElement<List<Map<String, dynamic>>>
    with CategorySpendingChartDataRef {
  _CategorySpendingChartDataProviderElement(super.provider);

  @override
  DateTime get month => (origin as CategorySpendingChartDataProvider).month;
}

String _$weeklySpendingChartDataHash() =>
    r'515fd9251bea811e316c89545db177715c4309b4';

/// Chart data for weekly spending trend (line chart)
///
/// Copied from [weeklySpendingChartData].
@ProviderFor(weeklySpendingChartData)
const weeklySpendingChartDataProvider = WeeklySpendingChartDataFamily();

/// Chart data for weekly spending trend (line chart)
///
/// Copied from [weeklySpendingChartData].
class WeeklySpendingChartDataFamily
    extends Family<AsyncValue<List<Map<String, dynamic>>>> {
  /// Chart data for weekly spending trend (line chart)
  ///
  /// Copied from [weeklySpendingChartData].
  const WeeklySpendingChartDataFamily();

  /// Chart data for weekly spending trend (line chart)
  ///
  /// Copied from [weeklySpendingChartData].
  WeeklySpendingChartDataProvider call(
    DateTime startDate,
  ) {
    return WeeklySpendingChartDataProvider(
      startDate,
    );
  }

  @override
  WeeklySpendingChartDataProvider getProviderOverride(
    covariant WeeklySpendingChartDataProvider provider,
  ) {
    return call(
      provider.startDate,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'weeklySpendingChartDataProvider';
}

/// Chart data for weekly spending trend (line chart)
///
/// Copied from [weeklySpendingChartData].
class WeeklySpendingChartDataProvider
    extends AutoDisposeFutureProvider<List<Map<String, dynamic>>> {
  /// Chart data for weekly spending trend (line chart)
  ///
  /// Copied from [weeklySpendingChartData].
  WeeklySpendingChartDataProvider(
    DateTime startDate,
  ) : this._internal(
          (ref) => weeklySpendingChartData(
            ref as WeeklySpendingChartDataRef,
            startDate,
          ),
          from: weeklySpendingChartDataProvider,
          name: r'weeklySpendingChartDataProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$weeklySpendingChartDataHash,
          dependencies: WeeklySpendingChartDataFamily._dependencies,
          allTransitiveDependencies:
              WeeklySpendingChartDataFamily._allTransitiveDependencies,
          startDate: startDate,
        );

  WeeklySpendingChartDataProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.startDate,
  }) : super.internal();

  final DateTime startDate;

  @override
  Override overrideWith(
    FutureOr<List<Map<String, dynamic>>> Function(
            WeeklySpendingChartDataRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: WeeklySpendingChartDataProvider._internal(
        (ref) => create(ref as WeeklySpendingChartDataRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        startDate: startDate,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<Map<String, dynamic>>> createElement() {
    return _WeeklySpendingChartDataProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is WeeklySpendingChartDataProvider &&
        other.startDate == startDate;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, startDate.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin WeeklySpendingChartDataRef
    on AutoDisposeFutureProviderRef<List<Map<String, dynamic>>> {
  /// The parameter `startDate` of this provider.
  DateTime get startDate;
}

class _WeeklySpendingChartDataProviderElement
    extends AutoDisposeFutureProviderElement<List<Map<String, dynamic>>>
    with WeeklySpendingChartDataRef {
  _WeeklySpendingChartDataProviderElement(super.provider);

  @override
  DateTime get startDate =>
      (origin as WeeklySpendingChartDataProvider).startDate;
}

String _$monthlySpendingAnalyticsHash() =>
    r'b9974c25615a278c5a4678ed35373a4c8fdf581a';

abstract class _$MonthlySpendingAnalytics
    extends BuildlessAutoDisposeAsyncNotifier<Map<String, dynamic>> {
  late final DateTime month;

  FutureOr<Map<String, dynamic>> build(
    DateTime month,
  );
}

/// Monthly spending analytics
///
/// Copied from [MonthlySpendingAnalytics].
@ProviderFor(MonthlySpendingAnalytics)
const monthlySpendingAnalyticsProvider = MonthlySpendingAnalyticsFamily();

/// Monthly spending analytics
///
/// Copied from [MonthlySpendingAnalytics].
class MonthlySpendingAnalyticsFamily
    extends Family<AsyncValue<Map<String, dynamic>>> {
  /// Monthly spending analytics
  ///
  /// Copied from [MonthlySpendingAnalytics].
  const MonthlySpendingAnalyticsFamily();

  /// Monthly spending analytics
  ///
  /// Copied from [MonthlySpendingAnalytics].
  MonthlySpendingAnalyticsProvider call(
    DateTime month,
  ) {
    return MonthlySpendingAnalyticsProvider(
      month,
    );
  }

  @override
  MonthlySpendingAnalyticsProvider getProviderOverride(
    covariant MonthlySpendingAnalyticsProvider provider,
  ) {
    return call(
      provider.month,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'monthlySpendingAnalyticsProvider';
}

/// Monthly spending analytics
///
/// Copied from [MonthlySpendingAnalytics].
class MonthlySpendingAnalyticsProvider
    extends AutoDisposeAsyncNotifierProviderImpl<MonthlySpendingAnalytics,
        Map<String, dynamic>> {
  /// Monthly spending analytics
  ///
  /// Copied from [MonthlySpendingAnalytics].
  MonthlySpendingAnalyticsProvider(
    DateTime month,
  ) : this._internal(
          () => MonthlySpendingAnalytics()..month = month,
          from: monthlySpendingAnalyticsProvider,
          name: r'monthlySpendingAnalyticsProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$monthlySpendingAnalyticsHash,
          dependencies: MonthlySpendingAnalyticsFamily._dependencies,
          allTransitiveDependencies:
              MonthlySpendingAnalyticsFamily._allTransitiveDependencies,
          month: month,
        );

  MonthlySpendingAnalyticsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.month,
  }) : super.internal();

  final DateTime month;

  @override
  FutureOr<Map<String, dynamic>> runNotifierBuild(
    covariant MonthlySpendingAnalytics notifier,
  ) {
    return notifier.build(
      month,
    );
  }

  @override
  Override overrideWith(MonthlySpendingAnalytics Function() create) {
    return ProviderOverride(
      origin: this,
      override: MonthlySpendingAnalyticsProvider._internal(
        () => create()..month = month,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        month: month,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<MonthlySpendingAnalytics,
      Map<String, dynamic>> createElement() {
    return _MonthlySpendingAnalyticsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is MonthlySpendingAnalyticsProvider && other.month == month;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, month.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin MonthlySpendingAnalyticsRef
    on AutoDisposeAsyncNotifierProviderRef<Map<String, dynamic>> {
  /// The parameter `month` of this provider.
  DateTime get month;
}

class _MonthlySpendingAnalyticsProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<MonthlySpendingAnalytics,
        Map<String, dynamic>> with MonthlySpendingAnalyticsRef {
  _MonthlySpendingAnalyticsProviderElement(super.provider);

  @override
  DateTime get month => (origin as MonthlySpendingAnalyticsProvider).month;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
