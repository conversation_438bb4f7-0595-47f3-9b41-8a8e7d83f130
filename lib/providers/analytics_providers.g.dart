// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'analytics_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$weeklySpendingTrendHash() =>
    r'df0c83dc4e665d875dd6c756702d3113cc1c2bc5';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Weekly spending trend
///
/// Copied from [weeklySpendingTrend].
@ProviderFor(weeklySpendingTrend)
const weeklySpendingTrendProvider = WeeklySpendingTrendFamily();

/// Weekly spending trend
///
/// Copied from [weeklySpendingTrend].
class WeeklySpendingTrendFamily
    extends Family<AsyncValue<List<Map<String, dynamic>>>> {
  /// Weekly spending trend
  ///
  /// Copied from [weeklySpendingTrend].
  const WeeklySpendingTrendFamily();

  /// Weekly spending trend
  ///
  /// Copied from [weeklySpendingTrend].
  WeeklySpendingTrendProvider call(
    DateTime startDate,
  ) {
    return WeeklySpendingTrendProvider(
      startDate,
    );
  }

  @override
  WeeklySpendingTrendProvider getProviderOverride(
    covariant WeeklySpendingTrendProvider provider,
  ) {
    return call(
      provider.startDate,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'weeklySpendingTrendProvider';
}

/// Weekly spending trend
///
/// Copied from [weeklySpendingTrend].
class WeeklySpendingTrendProvider
    extends AutoDisposeFutureProvider<List<Map<String, dynamic>>> {
  /// Weekly spending trend
  ///
  /// Copied from [weeklySpendingTrend].
  WeeklySpendingTrendProvider(
    DateTime startDate,
  ) : this._internal(
          (ref) => weeklySpendingTrend(
            ref as WeeklySpendingTrendRef,
            startDate,
          ),
          from: weeklySpendingTrendProvider,
          name: r'weeklySpendingTrendProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$weeklySpendingTrendHash,
          dependencies: WeeklySpendingTrendFamily._dependencies,
          allTransitiveDependencies:
              WeeklySpendingTrendFamily._allTransitiveDependencies,
          startDate: startDate,
        );

  WeeklySpendingTrendProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.startDate,
  }) : super.internal();

  final DateTime startDate;

  @override
  Override overrideWith(
    FutureOr<List<Map<String, dynamic>>> Function(
            WeeklySpendingTrendRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: WeeklySpendingTrendProvider._internal(
        (ref) => create(ref as WeeklySpendingTrendRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        startDate: startDate,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<Map<String, dynamic>>> createElement() {
    return _WeeklySpendingTrendProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is WeeklySpendingTrendProvider && other.startDate == startDate;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, startDate.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin WeeklySpendingTrendRef
    on AutoDisposeFutureProviderRef<List<Map<String, dynamic>>> {
  /// The parameter `startDate` of this provider.
  DateTime get startDate;
}

class _WeeklySpendingTrendProviderElement
    extends AutoDisposeFutureProviderElement<List<Map<String, dynamic>>>
    with WeeklySpendingTrendRef {
  _WeeklySpendingTrendProviderElement(super.provider);

  @override
  DateTime get startDate => (origin as WeeklySpendingTrendProvider).startDate;
}

String _$accountBalanceDistributionHash() =>
    r'f9d6e2b59775397814d870b123e04a9bd8379202';

/// Account balance distribution
///
/// Copied from [accountBalanceDistribution].
@ProviderFor(accountBalanceDistribution)
final accountBalanceDistributionProvider =
    AutoDisposeFutureProvider<List<Map<String, dynamic>>>.internal(
  accountBalanceDistribution,
  name: r'accountBalanceDistributionProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$accountBalanceDistributionHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef AccountBalanceDistributionRef
    = AutoDisposeFutureProviderRef<List<Map<String, dynamic>>>;
String _$spendingInsightsHash() => r'6fb47bb52ac1658db425e4b4282cc62b14ac7cd5';

/// Spending insights and patterns
///
/// Copied from [spendingInsights].
@ProviderFor(spendingInsights)
final spendingInsightsProvider =
    AutoDisposeFutureProvider<Map<String, dynamic>>.internal(
  spendingInsights,
  name: r'spendingInsightsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$spendingInsightsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef SpendingInsightsRef
    = AutoDisposeFutureProviderRef<Map<String, dynamic>>;
String _$transferAnalyticsHash() => r'7c2a251028ef4c16a48f67a412640c8d14c4c0c6';

/// Transfer analytics
///
/// Copied from [transferAnalytics].
@ProviderFor(transferAnalytics)
const transferAnalyticsProvider = TransferAnalyticsFamily();

/// Transfer analytics
///
/// Copied from [transferAnalytics].
class TransferAnalyticsFamily extends Family<AsyncValue<Map<String, dynamic>>> {
  /// Transfer analytics
  ///
  /// Copied from [transferAnalytics].
  const TransferAnalyticsFamily();

  /// Transfer analytics
  ///
  /// Copied from [transferAnalytics].
  TransferAnalyticsProvider call(
    DateTime month,
  ) {
    return TransferAnalyticsProvider(
      month,
    );
  }

  @override
  TransferAnalyticsProvider getProviderOverride(
    covariant TransferAnalyticsProvider provider,
  ) {
    return call(
      provider.month,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'transferAnalyticsProvider';
}

/// Transfer analytics
///
/// Copied from [transferAnalytics].
class TransferAnalyticsProvider
    extends AutoDisposeFutureProvider<Map<String, dynamic>> {
  /// Transfer analytics
  ///
  /// Copied from [transferAnalytics].
  TransferAnalyticsProvider(
    DateTime month,
  ) : this._internal(
          (ref) => transferAnalytics(
            ref as TransferAnalyticsRef,
            month,
          ),
          from: transferAnalyticsProvider,
          name: r'transferAnalyticsProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$transferAnalyticsHash,
          dependencies: TransferAnalyticsFamily._dependencies,
          allTransitiveDependencies:
              TransferAnalyticsFamily._allTransitiveDependencies,
          month: month,
        );

  TransferAnalyticsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.month,
  }) : super.internal();

  final DateTime month;

  @override
  Override overrideWith(
    FutureOr<Map<String, dynamic>> Function(TransferAnalyticsRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: TransferAnalyticsProvider._internal(
        (ref) => create(ref as TransferAnalyticsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        month: month,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Map<String, dynamic>> createElement() {
    return _TransferAnalyticsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is TransferAnalyticsProvider && other.month == month;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, month.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin TransferAnalyticsRef
    on AutoDisposeFutureProviderRef<Map<String, dynamic>> {
  /// The parameter `month` of this provider.
  DateTime get month;
}

class _TransferAnalyticsProviderElement
    extends AutoDisposeFutureProviderElement<Map<String, dynamic>>
    with TransferAnalyticsRef {
  _TransferAnalyticsProviderElement(super.provider);

  @override
  DateTime get month => (origin as TransferAnalyticsProvider).month;
}

String _$categorySpendingChartDataHash() =>
    r'ba9cc031f43313c23e0e5d8e20a21370bd693a43';

/// Chart data for spending by category (pie chart)
///
/// Copied from [categorySpendingChartData].
@ProviderFor(categorySpendingChartData)
const categorySpendingChartDataProvider = CategorySpendingChartDataFamily();

/// Chart data for spending by category (pie chart)
///
/// Copied from [categorySpendingChartData].
class CategorySpendingChartDataFamily
    extends Family<AsyncValue<List<Map<String, dynamic>>>> {
  /// Chart data for spending by category (pie chart)
  ///
  /// Copied from [categorySpendingChartData].
  const CategorySpendingChartDataFamily();

  /// Chart data for spending by category (pie chart)
  ///
  /// Copied from [categorySpendingChartData].
  CategorySpendingChartDataProvider call(
    DateTime month,
  ) {
    return CategorySpendingChartDataProvider(
      month,
    );
  }

  @override
  CategorySpendingChartDataProvider getProviderOverride(
    covariant CategorySpendingChartDataProvider provider,
  ) {
    return call(
      provider.month,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'categorySpendingChartDataProvider';
}

/// Chart data for spending by category (pie chart)
///
/// Copied from [categorySpendingChartData].
class CategorySpendingChartDataProvider
    extends AutoDisposeFutureProvider<List<Map<String, dynamic>>> {
  /// Chart data for spending by category (pie chart)
  ///
  /// Copied from [categorySpendingChartData].
  CategorySpendingChartDataProvider(
    DateTime month,
  ) : this._internal(
          (ref) => categorySpendingChartData(
            ref as CategorySpendingChartDataRef,
            month,
          ),
          from: categorySpendingChartDataProvider,
          name: r'categorySpendingChartDataProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$categorySpendingChartDataHash,
          dependencies: CategorySpendingChartDataFamily._dependencies,
          allTransitiveDependencies:
              CategorySpendingChartDataFamily._allTransitiveDependencies,
          month: month,
        );

  CategorySpendingChartDataProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.month,
  }) : super.internal();

  final DateTime month;

  @override
  Override overrideWith(
    FutureOr<List<Map<String, dynamic>>> Function(
            CategorySpendingChartDataRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: CategorySpendingChartDataProvider._internal(
        (ref) => create(ref as CategorySpendingChartDataRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        month: month,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<Map<String, dynamic>>> createElement() {
    return _CategorySpendingChartDataProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CategorySpendingChartDataProvider && other.month == month;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, month.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin CategorySpendingChartDataRef
    on AutoDisposeFutureProviderRef<List<Map<String, dynamic>>> {
  /// The parameter `month` of this provider.
  DateTime get month;
}

class _CategorySpendingChartDataProviderElement
    extends AutoDisposeFutureProviderElement<List<Map<String, dynamic>>>
    with CategorySpendingChartDataRef {
  _CategorySpendingChartDataProviderElement(super.provider);

  @override
  DateTime get month => (origin as CategorySpendingChartDataProvider).month;
}

String _$weeklySpendingChartDataHash() =>
    r'54f77eb7b094a004f9bf1b191a4f82a273b7c702';

/// Chart data for weekly spending trend (line chart)
///
/// Copied from [weeklySpendingChartData].
@ProviderFor(weeklySpendingChartData)
const weeklySpendingChartDataProvider = WeeklySpendingChartDataFamily();

/// Chart data for weekly spending trend (line chart)
///
/// Copied from [weeklySpendingChartData].
class WeeklySpendingChartDataFamily
    extends Family<AsyncValue<List<Map<String, dynamic>>>> {
  /// Chart data for weekly spending trend (line chart)
  ///
  /// Copied from [weeklySpendingChartData].
  const WeeklySpendingChartDataFamily();

  /// Chart data for weekly spending trend (line chart)
  ///
  /// Copied from [weeklySpendingChartData].
  WeeklySpendingChartDataProvider call(
    DateTime startDate,
  ) {
    return WeeklySpendingChartDataProvider(
      startDate,
    );
  }

  @override
  WeeklySpendingChartDataProvider getProviderOverride(
    covariant WeeklySpendingChartDataProvider provider,
  ) {
    return call(
      provider.startDate,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'weeklySpendingChartDataProvider';
}

/// Chart data for weekly spending trend (line chart)
///
/// Copied from [weeklySpendingChartData].
class WeeklySpendingChartDataProvider
    extends AutoDisposeFutureProvider<List<Map<String, dynamic>>> {
  /// Chart data for weekly spending trend (line chart)
  ///
  /// Copied from [weeklySpendingChartData].
  WeeklySpendingChartDataProvider(
    DateTime startDate,
  ) : this._internal(
          (ref) => weeklySpendingChartData(
            ref as WeeklySpendingChartDataRef,
            startDate,
          ),
          from: weeklySpendingChartDataProvider,
          name: r'weeklySpendingChartDataProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$weeklySpendingChartDataHash,
          dependencies: WeeklySpendingChartDataFamily._dependencies,
          allTransitiveDependencies:
              WeeklySpendingChartDataFamily._allTransitiveDependencies,
          startDate: startDate,
        );

  WeeklySpendingChartDataProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.startDate,
  }) : super.internal();

  final DateTime startDate;

  @override
  Override overrideWith(
    FutureOr<List<Map<String, dynamic>>> Function(
            WeeklySpendingChartDataRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: WeeklySpendingChartDataProvider._internal(
        (ref) => create(ref as WeeklySpendingChartDataRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        startDate: startDate,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<Map<String, dynamic>>> createElement() {
    return _WeeklySpendingChartDataProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is WeeklySpendingChartDataProvider &&
        other.startDate == startDate;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, startDate.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin WeeklySpendingChartDataRef
    on AutoDisposeFutureProviderRef<List<Map<String, dynamic>>> {
  /// The parameter `startDate` of this provider.
  DateTime get startDate;
}

class _WeeklySpendingChartDataProviderElement
    extends AutoDisposeFutureProviderElement<List<Map<String, dynamic>>>
    with WeeklySpendingChartDataRef {
  _WeeklySpendingChartDataProviderElement(super.provider);

  @override
  DateTime get startDate =>
      (origin as WeeklySpendingChartDataProvider).startDate;
}

String _$monthlySpendingAnalyticsHash() =>
    r'7dd0d383885785b33b0c760617ac5260ffb96c3d';

abstract class _$MonthlySpendingAnalytics
    extends BuildlessAutoDisposeAsyncNotifier<Map<String, dynamic>> {
  late final DateTime month;

  FutureOr<Map<String, dynamic>> build(
    DateTime month,
  );
}

/// Monthly spending analytics
///
/// Copied from [MonthlySpendingAnalytics].
@ProviderFor(MonthlySpendingAnalytics)
const monthlySpendingAnalyticsProvider = MonthlySpendingAnalyticsFamily();

/// Monthly spending analytics
///
/// Copied from [MonthlySpendingAnalytics].
class MonthlySpendingAnalyticsFamily
    extends Family<AsyncValue<Map<String, dynamic>>> {
  /// Monthly spending analytics
  ///
  /// Copied from [MonthlySpendingAnalytics].
  const MonthlySpendingAnalyticsFamily();

  /// Monthly spending analytics
  ///
  /// Copied from [MonthlySpendingAnalytics].
  MonthlySpendingAnalyticsProvider call(
    DateTime month,
  ) {
    return MonthlySpendingAnalyticsProvider(
      month,
    );
  }

  @override
  MonthlySpendingAnalyticsProvider getProviderOverride(
    covariant MonthlySpendingAnalyticsProvider provider,
  ) {
    return call(
      provider.month,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'monthlySpendingAnalyticsProvider';
}

/// Monthly spending analytics
///
/// Copied from [MonthlySpendingAnalytics].
class MonthlySpendingAnalyticsProvider
    extends AutoDisposeAsyncNotifierProviderImpl<MonthlySpendingAnalytics,
        Map<String, dynamic>> {
  /// Monthly spending analytics
  ///
  /// Copied from [MonthlySpendingAnalytics].
  MonthlySpendingAnalyticsProvider(
    DateTime month,
  ) : this._internal(
          () => MonthlySpendingAnalytics()..month = month,
          from: monthlySpendingAnalyticsProvider,
          name: r'monthlySpendingAnalyticsProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$monthlySpendingAnalyticsHash,
          dependencies: MonthlySpendingAnalyticsFamily._dependencies,
          allTransitiveDependencies:
              MonthlySpendingAnalyticsFamily._allTransitiveDependencies,
          month: month,
        );

  MonthlySpendingAnalyticsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.month,
  }) : super.internal();

  final DateTime month;

  @override
  FutureOr<Map<String, dynamic>> runNotifierBuild(
    covariant MonthlySpendingAnalytics notifier,
  ) {
    return notifier.build(
      month,
    );
  }

  @override
  Override overrideWith(MonthlySpendingAnalytics Function() create) {
    return ProviderOverride(
      origin: this,
      override: MonthlySpendingAnalyticsProvider._internal(
        () => create()..month = month,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        month: month,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<MonthlySpendingAnalytics,
      Map<String, dynamic>> createElement() {
    return _MonthlySpendingAnalyticsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is MonthlySpendingAnalyticsProvider && other.month == month;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, month.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin MonthlySpendingAnalyticsRef
    on AutoDisposeAsyncNotifierProviderRef<Map<String, dynamic>> {
  /// The parameter `month` of this provider.
  DateTime get month;
}

class _MonthlySpendingAnalyticsProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<MonthlySpendingAnalytics,
        Map<String, dynamic>> with MonthlySpendingAnalyticsRef {
  _MonthlySpendingAnalyticsProviderElement(super.provider);

  @override
  DateTime get month => (origin as MonthlySpendingAnalyticsProvider).month;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
