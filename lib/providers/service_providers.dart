import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/firebase_service.dart';
import '../services/auth_service.dart';
import '../services/connectivity_service.dart';
import '../services/sync_service.dart';

/// Firebase service provider
final firebaseServiceProvider = Provider<FirebaseService>((ref) {
  return FirebaseService.instance;
});

/// Authentication service provider
final authServiceProvider = Provider<AuthService>((ref) {
  return AuthService.instance;
});

/// Connectivity service provider
final connectivityServiceProvider = Provider<ConnectivityService>((ref) {
  return ConnectivityService.instance;
});

/// Sync service provider
final syncServiceProvider = Provider<SyncService>((ref) {
  return SyncService.instance;
});

/// Current user provider
final currentUserProvider = StreamProvider((ref) {
  final authService = ref.watch(authServiceProvider);
  return authService.authStateChanges;
});

/// Connection status provider
final connectionStatusProvider = StreamProvider<bool>((ref) {
  final connectivityService = ref.watch(connectivityServiceProvider);
  return connectivityService.connectionStatusStream;
});

/// Connection type provider
final connectionTypeProvider = StreamProvider<ConnectionType>((ref) {
  final connectivityService = ref.watch(connectivityServiceProvider);
  return connectivityService.connectionTypeStream;
});

/// Sync status provider
final syncStatusProvider = StreamProvider<SyncStatus>((ref) {
  final syncService = ref.watch(syncServiceProvider);
  return syncService.syncStatusStream;
});

/// Sync result provider
final syncResultProvider = StreamProvider<SyncResult>((ref) {
  final syncService = ref.watch(syncServiceProvider);
  return syncService.syncResultStream;
});

/// Pending sync count provider
final pendingSyncCountProvider = FutureProvider<int>((ref) async {
  final syncService = ref.watch(syncServiceProvider);
  return await syncService.getPendingSyncCount();
});

/// Manual sync provider
final manualSyncProvider = FutureProvider.family<SyncResult, void>((ref, _) async {
  final syncService = ref.watch(syncServiceProvider);
  return await syncService.manualSync();
});

/// Connection status string provider
final connectionStatusStringProvider = Provider<String>((ref) {
  final connectivityService = ref.watch(connectivityServiceProvider);
  return connectivityService.getConnectionStatusString();
});

/// Should sync provider (based on connection and user preferences)
final shouldSyncProvider = Provider<bool>((ref) {
  final connectivityService = ref.watch(connectivityServiceProvider);
  return connectivityService.shouldSync();
});

/// Is online provider
final isOnlineProvider = Provider<bool>((ref) {
  final connectivityService = ref.watch(connectivityServiceProvider);
  return connectivityService.isConnected;
});

/// Is authenticated provider
final isAuthenticatedProvider = Provider<bool>((ref) {
  final authService = ref.watch(authServiceProvider);
  return authService.isSignedIn;
});

/// User metadata provider
final userMetadataProvider = Provider<Map<String, dynamic>>((ref) {
  final authService = ref.watch(authServiceProvider);
  return authService.getUserMetadata();
});

/// Last sync time provider
final lastSyncTimeProvider = Provider<DateTime?>((ref) {
  final syncService = ref.watch(syncServiceProvider);
  return syncService.lastSyncTime;
});

/// Sync status text provider
final syncStatusTextProvider = Provider<String>((ref) {
  final syncService = ref.watch(syncServiceProvider);
  final status = syncService.currentStatus;
  
  switch (status) {
    case SyncStatus.idle:
      return 'Ready to sync';
    case SyncStatus.syncing:
      return 'Syncing...';
    case SyncStatus.success:
      return 'Sync completed';
    case SyncStatus.error:
      return 'Sync failed';
  }
});

/// Can sync provider (checks all conditions)
final canSyncProvider = Provider<bool>((ref) {
  final isOnline = ref.watch(isOnlineProvider);
  final isAuthenticated = ref.watch(isAuthenticatedProvider);
  final shouldSync = ref.watch(shouldSyncProvider);
  final syncService = ref.watch(syncServiceProvider);
  
  return isOnline && isAuthenticated && shouldSync && !syncService.isSyncing;
});

/// Sync indicator provider (for UI)
final syncIndicatorProvider = Provider<Map<String, dynamic>>((ref) {
  final syncService = ref.watch(syncServiceProvider);
  final pendingSyncCountAsync = ref.watch(pendingSyncCountProvider);
  final isOnline = ref.watch(isOnlineProvider);
  final canSync = ref.watch(canSyncProvider);
  
  final pendingCount = pendingSyncCountAsync.when(
    data: (count) => count,
    loading: () => 0,
    error: (_, __) => 0,
  );
  
  return {
    'status': syncService.currentStatus,
    'pendingCount': pendingCount,
    'isOnline': isOnline,
    'canSync': canSync,
    'lastSyncTime': syncService.lastSyncTime,
  };
});

/// Network quality provider
final networkQualityProvider = Provider<String>((ref) {
  final connectivityService = ref.watch(connectivityServiceProvider);
  return connectivityService.getConnectionQualityString();
});

/// Is high quality connection provider
final isHighQualityConnectionProvider = Provider<bool>((ref) {
  final connectivityService = ref.watch(connectivityServiceProvider);
  return connectivityService.isHighQuality;
});

/// Mobile sync enabled provider
final mobileSyncEnabledProvider = Provider<bool>((ref) {
  final connectivityService = ref.watch(connectivityServiceProvider);
  return connectivityService.isMobileSyncEnabled;
});

/// Connection info provider (comprehensive connection information)
final connectionInfoProvider = Provider<Map<String, dynamic>>((ref) {
  final connectivityService = ref.watch(connectivityServiceProvider);
  
  return {
    'isConnected': connectivityService.isConnected,
    'connectionType': connectivityService.connectionType,
    'connectionQuality': connectivityService.connectionQuality,
    'isWiFi': connectivityService.isWiFi,
    'isMobile': connectivityService.isMobile,
    'isHighQuality': connectivityService.isHighQuality,
    'shouldSync': connectivityService.shouldSync(),
    'statusString': connectivityService.getConnectionStatusString(),
    'qualityString': connectivityService.getConnectionQualityString(),
  };
});

/// Sync statistics provider
final syncStatisticsProvider = Provider<Map<String, dynamic>>((ref) {
  final syncService = ref.watch(syncServiceProvider);
  final pendingSyncCountAsync = ref.watch(pendingSyncCountProvider);
  
  final pendingCount = pendingSyncCountAsync.when(
    data: (count) => count,
    loading: () => 0,
    error: (_, __) => 0,
  );
  
  return {
    'currentStatus': syncService.currentStatus,
    'lastSyncTime': syncService.lastSyncTime,
    'pendingCount': pendingCount,
    'isSyncing': syncService.isSyncing,
  };
});

/// Force sync provider (for admin/debug purposes)
final forceSyncProvider = FutureProvider.family<SyncResult, void>((ref, _) async {
  final syncService = ref.watch(syncServiceProvider);
  return await syncService.forceSync();
});

/// Refresh sync count provider
final refreshSyncCountProvider = FutureProvider.family<int, void>((ref, _) async {
  final syncService = ref.watch(syncServiceProvider);
  return await syncService.getPendingSyncCount();
});

/// Authentication error provider
final authErrorProvider = StateProvider<String?>((ref) => null);

/// Sync error provider
final syncErrorProvider = StateProvider<String?>((ref) => null);

/// Connection error provider
final connectionErrorProvider = StateProvider<String?>((ref) => null);

/// Global error provider (combines all error sources)
final globalErrorProvider = Provider<String?>((ref) {
  final authError = ref.watch(authErrorProvider);
  final syncError = ref.watch(syncErrorProvider);
  final connectionError = ref.watch(connectionErrorProvider);
  
  // Return the first non-null error
  return authError ?? syncError ?? connectionError;
});

/// Clear all errors provider
final clearAllErrorsProvider = Provider<void>((ref) {
  ref.read(authErrorProvider.notifier).state = null;
  ref.read(syncErrorProvider.notifier).state = null;
  ref.read(connectionErrorProvider.notifier).state = null;
});

/// Service health provider (overall health check)
final serviceHealthProvider = Provider<Map<String, bool>>((ref) {
  final authService = ref.watch(authServiceProvider);
  final connectivityService = ref.watch(connectivityServiceProvider);
  final syncService = ref.watch(syncServiceProvider);
  
  return {
    'auth': authService.isSignedIn,
    'connectivity': connectivityService.isConnected,
    'sync': syncService.currentStatus != SyncStatus.error,
    'overall': authService.isSignedIn && 
               connectivityService.isConnected && 
               syncService.currentStatus != SyncStatus.error,
  };
});
