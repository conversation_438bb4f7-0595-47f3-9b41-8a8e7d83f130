import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../data/models/account.dart';
import '../data/models/account_balance.dart';
import '../data/repositories/account_repository.dart';
import 'database_providers.dart';

/// Provider for all accounts
final accountsProvider = FutureProvider<List<Account>>((ref) async {
  final repository = ref.watch(accountRepositoryProvider);
  return repository.getAccounts();
});

/// Provider for accounts with balances
final accountsWithBalancesProvider = FutureProvider<List<AccountBalance>>((ref) async {
  final repository = ref.watch(accountRepositoryProvider);
  return repository.getAccountsWithBalances();
});

/// Provider for total balance
final totalBalanceProvider = FutureProvider<double>((ref) async {
  final repository = ref.watch(accountRepositoryProvider);
  return repository.getTotalBalance();
});

/// Provider for a specific account
final accountProvider = FutureProvider.family<Account?, String>((ref, accountId) async {
  final repository = ref.watch(accountRepositoryProvider);
  return repository.getAccount(accountId);
});

/// Provider for account balance
final accountBalanceProvider = FutureProvider.family<AccountBalance?, String>((ref, accountId) async {
  final repository = ref.watch(accountRepositoryProvider);
  return repository.getAccountBalance(accountId);
});

/// Notifier for managing account state
class AccountNotifier extends StateNotifier<AsyncValue<List<Account>>> {
  AccountNotifier(this._repository) : super(const AsyncValue.loading()) {
    _loadAccounts();
  }

  final AccountRepository _repository;

  Future<void> _loadAccounts() async {
    try {
      final accounts = await _repository.getAccounts();
      state = AsyncValue.data(accounts);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> createAccount({
    required String name,
    required AccountType type,
    required double initialBalance,
  }) async {
    try {
      await _repository.createAccount(
        name: name,
        type: type,
        initialBalance: initialBalance,
      );
      await _loadAccounts(); // Refresh the list
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
      rethrow;
    }
  }

  Future<void> updateAccount(Account account) async {
    try {
      await _repository.updateAccount(account);
      await _loadAccounts(); // Refresh the list
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
      rethrow;
    }
  }

  Future<void> deleteAccount(String accountId) async {
    try {
      await _repository.deleteAccount(accountId);
      await _loadAccounts(); // Refresh the list
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
      rethrow;
    }
  }

  Future<void> adjustAccountBalance({
    required String accountId,
    required double adjustmentAmount,
    required String reason,
  }) async {
    try {
      await _repository.adjustAccountBalance(
        accountId: accountId,
        adjustmentAmount: adjustmentAmount,
        reason: reason,
      );
      await _loadAccounts(); // Refresh the list
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
      rethrow;
    }
  }

  void refresh() {
    _loadAccounts();
  }
}

/// Provider for account notifier
final accountNotifierProvider = StateNotifierProvider<AccountNotifier, AsyncValue<List<Account>>>((ref) {
  final repository = ref.watch(accountRepositoryProvider);
  return AccountNotifier(repository);
});

/// Notifier for managing account balances state
class AccountBalanceNotifier extends StateNotifier<AsyncValue<List<AccountBalance>>> {
  AccountBalanceNotifier(this._repository) : super(const AsyncValue.loading()) {
    _loadAccountBalances();
  }

  final AccountRepository _repository;

  Future<void> _loadAccountBalances() async {
    try {
      final accountBalances = await _repository.getAccountsWithBalances();
      state = AsyncValue.data(accountBalances);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  void refresh() {
    _loadAccountBalances();
  }
}

/// Provider for account balance notifier
final accountBalanceNotifierProvider = StateNotifierProvider<AccountBalanceNotifier, AsyncValue<List<AccountBalance>>>((ref) {
  final repository = ref.watch(accountRepositoryProvider);
  return AccountBalanceNotifier(repository);
});

/// Notifier for managing total balance state
class TotalBalanceNotifier extends StateNotifier<AsyncValue<double>> {
  TotalBalanceNotifier(this._repository) : super(const AsyncValue.loading()) {
    _loadTotalBalance();
  }

  final AccountRepository _repository;

  Future<void> _loadTotalBalance() async {
    try {
      final totalBalance = await _repository.getTotalBalance();
      state = AsyncValue.data(totalBalance);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  void refresh() {
    _loadTotalBalance();
  }
}

/// Provider for total balance notifier
final totalBalanceNotifierProvider = StateNotifierProvider<TotalBalanceNotifier, AsyncValue<double>>((ref) {
  final repository = ref.watch(accountRepositoryProvider);
  return TotalBalanceNotifier(repository);
});
