// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'category_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$categoryRepositoryHash() =>
    r'2c639f6980a7270515ebbdc08e1c3a4bf104027f';

/// Category repository provider
///
/// Copied from [categoryRepository].
@ProviderFor(categoryRepository)
final categoryRepositoryProvider =
    AutoDisposeProvider<CategoryRepository>.internal(
  categoryRepository,
  name: r'categoryRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$categoryRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CategoryRepositoryRef = AutoDisposeProviderRef<CategoryRepository>;
String _$categoryByIdHash() => r'e94b5ffd7331af43abd8d07c7d2682cef914fcdd';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Category by ID provider
///
/// Copied from [categoryById].
@ProviderFor(categoryById)
const categoryByIdProvider = CategoryByIdFamily();

/// Category by ID provider
///
/// Copied from [categoryById].
class CategoryByIdFamily extends Family<AsyncValue<Category?>> {
  /// Category by ID provider
  ///
  /// Copied from [categoryById].
  const CategoryByIdFamily();

  /// Category by ID provider
  ///
  /// Copied from [categoryById].
  CategoryByIdProvider call(
    String id,
  ) {
    return CategoryByIdProvider(
      id,
    );
  }

  @override
  CategoryByIdProvider getProviderOverride(
    covariant CategoryByIdProvider provider,
  ) {
    return call(
      provider.id,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'categoryByIdProvider';
}

/// Category by ID provider
///
/// Copied from [categoryById].
class CategoryByIdProvider extends AutoDisposeFutureProvider<Category?> {
  /// Category by ID provider
  ///
  /// Copied from [categoryById].
  CategoryByIdProvider(
    String id,
  ) : this._internal(
          (ref) => categoryById(
            ref as CategoryByIdRef,
            id,
          ),
          from: categoryByIdProvider,
          name: r'categoryByIdProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$categoryByIdHash,
          dependencies: CategoryByIdFamily._dependencies,
          allTransitiveDependencies:
              CategoryByIdFamily._allTransitiveDependencies,
          id: id,
        );

  CategoryByIdProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.id,
  }) : super.internal();

  final String id;

  @override
  Override overrideWith(
    FutureOr<Category?> Function(CategoryByIdRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: CategoryByIdProvider._internal(
        (ref) => create(ref as CategoryByIdRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        id: id,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Category?> createElement() {
    return _CategoryByIdProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CategoryByIdProvider && other.id == id;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, id.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin CategoryByIdRef on AutoDisposeFutureProviderRef<Category?> {
  /// The parameter `id` of this provider.
  String get id;
}

class _CategoryByIdProviderElement
    extends AutoDisposeFutureProviderElement<Category?> with CategoryByIdRef {
  _CategoryByIdProviderElement(super.provider);

  @override
  String get id => (origin as CategoryByIdProvider).id;
}

String _$categorySuggestionsHash() =>
    r'4b535bb2566bb36fa86e09cf9ef7298c6e7e7e15';

/// Category suggestions provider - suggests categories based on transaction description
///
/// Copied from [categorySuggestions].
@ProviderFor(categorySuggestions)
const categorySuggestionsProvider = CategorySuggestionsFamily();

/// Category suggestions provider - suggests categories based on transaction description
///
/// Copied from [categorySuggestions].
class CategorySuggestionsFamily extends Family<AsyncValue<List<Category>>> {
  /// Category suggestions provider - suggests categories based on transaction description
  ///
  /// Copied from [categorySuggestions].
  const CategorySuggestionsFamily();

  /// Category suggestions provider - suggests categories based on transaction description
  ///
  /// Copied from [categorySuggestions].
  CategorySuggestionsProvider call(
    String description,
  ) {
    return CategorySuggestionsProvider(
      description,
    );
  }

  @override
  CategorySuggestionsProvider getProviderOverride(
    covariant CategorySuggestionsProvider provider,
  ) {
    return call(
      provider.description,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'categorySuggestionsProvider';
}

/// Category suggestions provider - suggests categories based on transaction description
///
/// Copied from [categorySuggestions].
class CategorySuggestionsProvider
    extends AutoDisposeFutureProvider<List<Category>> {
  /// Category suggestions provider - suggests categories based on transaction description
  ///
  /// Copied from [categorySuggestions].
  CategorySuggestionsProvider(
    String description,
  ) : this._internal(
          (ref) => categorySuggestions(
            ref as CategorySuggestionsRef,
            description,
          ),
          from: categorySuggestionsProvider,
          name: r'categorySuggestionsProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$categorySuggestionsHash,
          dependencies: CategorySuggestionsFamily._dependencies,
          allTransitiveDependencies:
              CategorySuggestionsFamily._allTransitiveDependencies,
          description: description,
        );

  CategorySuggestionsProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.description,
  }) : super.internal();

  final String description;

  @override
  Override overrideWith(
    FutureOr<List<Category>> Function(CategorySuggestionsRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: CategorySuggestionsProvider._internal(
        (ref) => create(ref as CategorySuggestionsRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        description: description,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<Category>> createElement() {
    return _CategorySuggestionsProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CategorySuggestionsProvider &&
        other.description == description;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, description.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin CategorySuggestionsRef on AutoDisposeFutureProviderRef<List<Category>> {
  /// The parameter `description` of this provider.
  String get description;
}

class _CategorySuggestionsProviderElement
    extends AutoDisposeFutureProviderElement<List<Category>>
    with CategorySuggestionsRef {
  _CategorySuggestionsProviderElement(super.provider);

  @override
  String get description => (origin as CategorySuggestionsProvider).description;
}

String _$categoriesWithBudgetsHash() =>
    r'b24de1af2f7106f454dc723188f94bb258e88fa5';

/// Categories with budgets provider
///
/// Copied from [categoriesWithBudgets].
@ProviderFor(categoriesWithBudgets)
final categoriesWithBudgetsProvider =
    AutoDisposeFutureProvider<List<Category>>.internal(
  categoriesWithBudgets,
  name: r'categoriesWithBudgetsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$categoriesWithBudgetsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CategoriesWithBudgetsRef = AutoDisposeFutureProviderRef<List<Category>>;
String _$categorySearchHash() => r'6dde83c3a4b1b86e399cd7343bc07596d217978d';

/// Category search provider
///
/// Copied from [categorySearch].
@ProviderFor(categorySearch)
const categorySearchProvider = CategorySearchFamily();

/// Category search provider
///
/// Copied from [categorySearch].
class CategorySearchFamily extends Family<AsyncValue<List<Category>>> {
  /// Category search provider
  ///
  /// Copied from [categorySearch].
  const CategorySearchFamily();

  /// Category search provider
  ///
  /// Copied from [categorySearch].
  CategorySearchProvider call(
    String query,
  ) {
    return CategorySearchProvider(
      query,
    );
  }

  @override
  CategorySearchProvider getProviderOverride(
    covariant CategorySearchProvider provider,
  ) {
    return call(
      provider.query,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'categorySearchProvider';
}

/// Category search provider
///
/// Copied from [categorySearch].
class CategorySearchProvider extends AutoDisposeFutureProvider<List<Category>> {
  /// Category search provider
  ///
  /// Copied from [categorySearch].
  CategorySearchProvider(
    String query,
  ) : this._internal(
          (ref) => categorySearch(
            ref as CategorySearchRef,
            query,
          ),
          from: categorySearchProvider,
          name: r'categorySearchProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$categorySearchHash,
          dependencies: CategorySearchFamily._dependencies,
          allTransitiveDependencies:
              CategorySearchFamily._allTransitiveDependencies,
          query: query,
        );

  CategorySearchProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.query,
  }) : super.internal();

  final String query;

  @override
  Override overrideWith(
    FutureOr<List<Category>> Function(CategorySearchRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: CategorySearchProvider._internal(
        (ref) => create(ref as CategorySearchRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        query: query,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<Category>> createElement() {
    return _CategorySearchProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is CategorySearchProvider && other.query == query;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, query.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin CategorySearchRef on AutoDisposeFutureProviderRef<List<Category>> {
  /// The parameter `query` of this provider.
  String get query;
}

class _CategorySearchProviderElement
    extends AutoDisposeFutureProviderElement<List<Category>>
    with CategorySearchRef {
  _CategorySearchProviderElement(super.provider);

  @override
  String get query => (origin as CategorySearchProvider).query;
}

String _$defaultCategoriesHash() => r'cd45ea517f4a2904a63588ae5ea724140d9fcf1c';

/// Default categories provider
///
/// Copied from [defaultCategories].
@ProviderFor(defaultCategories)
final defaultCategoriesProvider = AutoDisposeProvider<List<Category>>.internal(
  defaultCategories,
  name: r'defaultCategoriesProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$defaultCategoriesHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef DefaultCategoriesRef = AutoDisposeProviderRef<List<Category>>;
String _$categoryIconsHash() => r'5efefff36acf4e72b06fd422d9257583815fbe0d';

/// Category icons provider
///
/// Copied from [categoryIcons].
@ProviderFor(categoryIcons)
final categoryIconsProvider = AutoDisposeProvider<List<String>>.internal(
  categoryIcons,
  name: r'categoryIconsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$categoryIconsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CategoryIconsRef = AutoDisposeProviderRef<List<String>>;
String _$categoryColorsHash() => r'e523647f6900afb73c16833ecf4235e65af21d86';

/// Category colors provider
///
/// Copied from [categoryColors].
@ProviderFor(categoryColors)
final categoryColorsProvider = AutoDisposeProvider<List<String>>.internal(
  categoryColors,
  name: r'categoryColorsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$categoryColorsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef CategoryColorsRef = AutoDisposeProviderRef<List<String>>;
String _$categoriesHash() => r'709a3c4194984160a09c7981bd93d30836463ece';

/// Categories provider - provides all active categories
///
/// Copied from [Categories].
@ProviderFor(Categories)
final categoriesProvider =
    AutoDisposeAsyncNotifierProvider<Categories, List<Category>>.internal(
  Categories.new,
  name: r'categoriesProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$categoriesHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Categories = AutoDisposeAsyncNotifier<List<Category>>;
String _$smartCategoryAssignmentHash() =>
    r'767eec356bec3402f2cf54c8a7fa7c85dc5d3bb4';

/// Smart category assignment provider
///
/// Copied from [SmartCategoryAssignment].
@ProviderFor(SmartCategoryAssignment)
final smartCategoryAssignmentProvider =
    AutoDisposeNotifierProvider<SmartCategoryAssignment, String?>.internal(
  SmartCategoryAssignment.new,
  name: r'smartCategoryAssignmentProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$smartCategoryAssignmentHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$SmartCategoryAssignment = AutoDisposeNotifier<String?>;
String _$categoryUsageStatsHash() =>
    r'16bd9d49bf094dfd09ed3af437c7e1dfe33938db';

/// Category usage statistics provider
///
/// Copied from [CategoryUsageStats].
@ProviderFor(CategoryUsageStats)
final categoryUsageStatsProvider = AutoDisposeAsyncNotifierProvider<
    CategoryUsageStats, Map<String, int>>.internal(
  CategoryUsageStats.new,
  name: r'categoryUsageStatsProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$categoryUsageStatsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$CategoryUsageStats = AutoDisposeAsyncNotifier<Map<String, int>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
