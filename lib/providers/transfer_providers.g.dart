// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transfer_providers.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$transferRepositoryHash() =>
    r'dca683b539c36b96473fbda0878dcc0573874185';

/// Transfer repository provider
///
/// Copied from [transferRepository].
@ProviderFor(transferRepository)
final transferRepositoryProvider =
    AutoDisposeProvider<TransferRepository>.internal(
  transferRepository,
  name: r'transferRepositoryProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$transferRepositoryHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef TransferRepositoryRef = AutoDisposeProviderRef<TransferRepository>;
String _$transferByIdHash() => r'd4df5a81ea39a2174d12ca7b18de527b9ed2a51b';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// Transfer by ID provider
///
/// Copied from [transferById].
@ProviderFor(transferById)
const transferByIdProvider = TransferByIdFamily();

/// Transfer by ID provider
///
/// Copied from [transferById].
class TransferByIdFamily extends Family<AsyncValue<Transfer?>> {
  /// Transfer by ID provider
  ///
  /// Copied from [transferById].
  const TransferByIdFamily();

  /// Transfer by ID provider
  ///
  /// Copied from [transferById].
  TransferByIdProvider call(
    String id,
  ) {
    return TransferByIdProvider(
      id,
    );
  }

  @override
  TransferByIdProvider getProviderOverride(
    covariant TransferByIdProvider provider,
  ) {
    return call(
      provider.id,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'transferByIdProvider';
}

/// Transfer by ID provider
///
/// Copied from [transferById].
class TransferByIdProvider extends AutoDisposeFutureProvider<Transfer?> {
  /// Transfer by ID provider
  ///
  /// Copied from [transferById].
  TransferByIdProvider(
    String id,
  ) : this._internal(
          (ref) => transferById(
            ref as TransferByIdRef,
            id,
          ),
          from: transferByIdProvider,
          name: r'transferByIdProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$transferByIdHash,
          dependencies: TransferByIdFamily._dependencies,
          allTransitiveDependencies:
              TransferByIdFamily._allTransitiveDependencies,
          id: id,
        );

  TransferByIdProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.id,
  }) : super.internal();

  final String id;

  @override
  Override overrideWith(
    FutureOr<Transfer?> Function(TransferByIdRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: TransferByIdProvider._internal(
        (ref) => create(ref as TransferByIdRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        id: id,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Transfer?> createElement() {
    return _TransferByIdProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is TransferByIdProvider && other.id == id;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, id.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin TransferByIdRef on AutoDisposeFutureProviderRef<Transfer?> {
  /// The parameter `id` of this provider.
  String get id;
}

class _TransferByIdProviderElement
    extends AutoDisposeFutureProviderElement<Transfer?> with TransferByIdRef {
  _TransferByIdProviderElement(super.provider);

  @override
  String get id => (origin as TransferByIdProvider).id;
}

String _$transfersForAccountHash() =>
    r'02453cd432a3784d96063381144a5658d73b94b9';

/// Transfers for account provider
///
/// Copied from [transfersForAccount].
@ProviderFor(transfersForAccount)
const transfersForAccountProvider = TransfersForAccountFamily();

/// Transfers for account provider
///
/// Copied from [transfersForAccount].
class TransfersForAccountFamily extends Family<AsyncValue<List<Transfer>>> {
  /// Transfers for account provider
  ///
  /// Copied from [transfersForAccount].
  const TransfersForAccountFamily();

  /// Transfers for account provider
  ///
  /// Copied from [transfersForAccount].
  TransfersForAccountProvider call(
    String accountId,
  ) {
    return TransfersForAccountProvider(
      accountId,
    );
  }

  @override
  TransfersForAccountProvider getProviderOverride(
    covariant TransfersForAccountProvider provider,
  ) {
    return call(
      provider.accountId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'transfersForAccountProvider';
}

/// Transfers for account provider
///
/// Copied from [transfersForAccount].
class TransfersForAccountProvider
    extends AutoDisposeFutureProvider<List<Transfer>> {
  /// Transfers for account provider
  ///
  /// Copied from [transfersForAccount].
  TransfersForAccountProvider(
    String accountId,
  ) : this._internal(
          (ref) => transfersForAccount(
            ref as TransfersForAccountRef,
            accountId,
          ),
          from: transfersForAccountProvider,
          name: r'transfersForAccountProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$transfersForAccountHash,
          dependencies: TransfersForAccountFamily._dependencies,
          allTransitiveDependencies:
              TransfersForAccountFamily._allTransitiveDependencies,
          accountId: accountId,
        );

  TransfersForAccountProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.accountId,
  }) : super.internal();

  final String accountId;

  @override
  Override overrideWith(
    FutureOr<List<Transfer>> Function(TransfersForAccountRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: TransfersForAccountProvider._internal(
        (ref) => create(ref as TransfersForAccountRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        accountId: accountId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<Transfer>> createElement() {
    return _TransfersForAccountProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is TransfersForAccountProvider && other.accountId == accountId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, accountId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin TransfersForAccountRef on AutoDisposeFutureProviderRef<List<Transfer>> {
  /// The parameter `accountId` of this provider.
  String get accountId;
}

class _TransfersForAccountProviderElement
    extends AutoDisposeFutureProviderElement<List<Transfer>>
    with TransfersForAccountRef {
  _TransfersForAccountProviderElement(super.provider);

  @override
  String get accountId => (origin as TransfersForAccountProvider).accountId;
}

String _$recentTransfersHash() => r'f90d6d61d84213398439c03d3e2ca8631c65a088';

/// Recent transfers provider
///
/// Copied from [recentTransfers].
@ProviderFor(recentTransfers)
const recentTransfersProvider = RecentTransfersFamily();

/// Recent transfers provider
///
/// Copied from [recentTransfers].
class RecentTransfersFamily extends Family<AsyncValue<List<Transfer>>> {
  /// Recent transfers provider
  ///
  /// Copied from [recentTransfers].
  const RecentTransfersFamily();

  /// Recent transfers provider
  ///
  /// Copied from [recentTransfers].
  RecentTransfersProvider call({
    int days = 30,
  }) {
    return RecentTransfersProvider(
      days: days,
    );
  }

  @override
  RecentTransfersProvider getProviderOverride(
    covariant RecentTransfersProvider provider,
  ) {
    return call(
      days: provider.days,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'recentTransfersProvider';
}

/// Recent transfers provider
///
/// Copied from [recentTransfers].
class RecentTransfersProvider
    extends AutoDisposeFutureProvider<List<Transfer>> {
  /// Recent transfers provider
  ///
  /// Copied from [recentTransfers].
  RecentTransfersProvider({
    int days = 30,
  }) : this._internal(
          (ref) => recentTransfers(
            ref as RecentTransfersRef,
            days: days,
          ),
          from: recentTransfersProvider,
          name: r'recentTransfersProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$recentTransfersHash,
          dependencies: RecentTransfersFamily._dependencies,
          allTransitiveDependencies:
              RecentTransfersFamily._allTransitiveDependencies,
          days: days,
        );

  RecentTransfersProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.days,
  }) : super.internal();

  final int days;

  @override
  Override overrideWith(
    FutureOr<List<Transfer>> Function(RecentTransfersRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: RecentTransfersProvider._internal(
        (ref) => create(ref as RecentTransfersRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        days: days,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<Transfer>> createElement() {
    return _RecentTransfersProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is RecentTransfersProvider && other.days == days;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, days.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin RecentTransfersRef on AutoDisposeFutureProviderRef<List<Transfer>> {
  /// The parameter `days` of this provider.
  int get days;
}

class _RecentTransfersProviderElement
    extends AutoDisposeFutureProviderElement<List<Transfer>>
    with RecentTransfersRef {
  _RecentTransfersProviderElement(super.provider);

  @override
  int get days => (origin as RecentTransfersProvider).days;
}

String _$transferStatsForAccountHash() =>
    r'96113fd0b36d7c5081a820b4cdd12c227eaddaba';

/// Transfer statistics for account provider
///
/// Copied from [transferStatsForAccount].
@ProviderFor(transferStatsForAccount)
const transferStatsForAccountProvider = TransferStatsForAccountFamily();

/// Transfer statistics for account provider
///
/// Copied from [transferStatsForAccount].
class TransferStatsForAccountFamily
    extends Family<AsyncValue<Map<String, dynamic>>> {
  /// Transfer statistics for account provider
  ///
  /// Copied from [transferStatsForAccount].
  const TransferStatsForAccountFamily();

  /// Transfer statistics for account provider
  ///
  /// Copied from [transferStatsForAccount].
  TransferStatsForAccountProvider call(
    String accountId,
  ) {
    return TransferStatsForAccountProvider(
      accountId,
    );
  }

  @override
  TransferStatsForAccountProvider getProviderOverride(
    covariant TransferStatsForAccountProvider provider,
  ) {
    return call(
      provider.accountId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'transferStatsForAccountProvider';
}

/// Transfer statistics for account provider
///
/// Copied from [transferStatsForAccount].
class TransferStatsForAccountProvider
    extends AutoDisposeFutureProvider<Map<String, dynamic>> {
  /// Transfer statistics for account provider
  ///
  /// Copied from [transferStatsForAccount].
  TransferStatsForAccountProvider(
    String accountId,
  ) : this._internal(
          (ref) => transferStatsForAccount(
            ref as TransferStatsForAccountRef,
            accountId,
          ),
          from: transferStatsForAccountProvider,
          name: r'transferStatsForAccountProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$transferStatsForAccountHash,
          dependencies: TransferStatsForAccountFamily._dependencies,
          allTransitiveDependencies:
              TransferStatsForAccountFamily._allTransitiveDependencies,
          accountId: accountId,
        );

  TransferStatsForAccountProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.accountId,
  }) : super.internal();

  final String accountId;

  @override
  Override overrideWith(
    FutureOr<Map<String, dynamic>> Function(TransferStatsForAccountRef provider)
        create,
  ) {
    return ProviderOverride(
      origin: this,
      override: TransferStatsForAccountProvider._internal(
        (ref) => create(ref as TransferStatsForAccountRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        accountId: accountId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<Map<String, dynamic>> createElement() {
    return _TransferStatsForAccountProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is TransferStatsForAccountProvider &&
        other.accountId == accountId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, accountId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin TransferStatsForAccountRef
    on AutoDisposeFutureProviderRef<Map<String, dynamic>> {
  /// The parameter `accountId` of this provider.
  String get accountId;
}

class _TransferStatsForAccountProviderElement
    extends AutoDisposeFutureProviderElement<Map<String, dynamic>>
    with TransferStatsForAccountRef {
  _TransferStatsForAccountProviderElement(super.provider);

  @override
  String get accountId => (origin as TransferStatsForAccountProvider).accountId;
}

String _$transferSearchHash() => r'4b8e78b5777a48a11bdd298ade4b1cb5fe1902d5';

/// Transfer search provider
///
/// Copied from [transferSearch].
@ProviderFor(transferSearch)
const transferSearchProvider = TransferSearchFamily();

/// Transfer search provider
///
/// Copied from [transferSearch].
class TransferSearchFamily extends Family<AsyncValue<List<Transfer>>> {
  /// Transfer search provider
  ///
  /// Copied from [transferSearch].
  const TransferSearchFamily();

  /// Transfer search provider
  ///
  /// Copied from [transferSearch].
  TransferSearchProvider call(
    String query,
  ) {
    return TransferSearchProvider(
      query,
    );
  }

  @override
  TransferSearchProvider getProviderOverride(
    covariant TransferSearchProvider provider,
  ) {
    return call(
      provider.query,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'transferSearchProvider';
}

/// Transfer search provider
///
/// Copied from [transferSearch].
class TransferSearchProvider extends AutoDisposeFutureProvider<List<Transfer>> {
  /// Transfer search provider
  ///
  /// Copied from [transferSearch].
  TransferSearchProvider(
    String query,
  ) : this._internal(
          (ref) => transferSearch(
            ref as TransferSearchRef,
            query,
          ),
          from: transferSearchProvider,
          name: r'transferSearchProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$transferSearchHash,
          dependencies: TransferSearchFamily._dependencies,
          allTransitiveDependencies:
              TransferSearchFamily._allTransitiveDependencies,
          query: query,
        );

  TransferSearchProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.query,
  }) : super.internal();

  final String query;

  @override
  Override overrideWith(
    FutureOr<List<Transfer>> Function(TransferSearchRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: TransferSearchProvider._internal(
        (ref) => create(ref as TransferSearchRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        query: query,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<List<Transfer>> createElement() {
    return _TransferSearchProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is TransferSearchProvider && other.query == query;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, query.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin TransferSearchRef on AutoDisposeFutureProviderRef<List<Transfer>> {
  /// The parameter `query` of this provider.
  String get query;
}

class _TransferSearchProviderElement
    extends AutoDisposeFutureProviderElement<List<Transfer>>
    with TransferSearchRef {
  _TransferSearchProviderElement(super.provider);

  @override
  String get query => (origin as TransferSearchProvider).query;
}

String _$monthlyTransferAmountHash() =>
    r'91a86683e09cc7cb01eb66b93b3ab1d1b726befe';

/// Monthly transfer amount provider
///
/// Copied from [monthlyTransferAmount].
@ProviderFor(monthlyTransferAmount)
const monthlyTransferAmountProvider = MonthlyTransferAmountFamily();

/// Monthly transfer amount provider
///
/// Copied from [monthlyTransferAmount].
class MonthlyTransferAmountFamily extends Family<AsyncValue<double>> {
  /// Monthly transfer amount provider
  ///
  /// Copied from [monthlyTransferAmount].
  const MonthlyTransferAmountFamily();

  /// Monthly transfer amount provider
  ///
  /// Copied from [monthlyTransferAmount].
  MonthlyTransferAmountProvider call(
    DateTime month,
    String accountId,
  ) {
    return MonthlyTransferAmountProvider(
      month,
      accountId,
    );
  }

  @override
  MonthlyTransferAmountProvider getProviderOverride(
    covariant MonthlyTransferAmountProvider provider,
  ) {
    return call(
      provider.month,
      provider.accountId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'monthlyTransferAmountProvider';
}

/// Monthly transfer amount provider
///
/// Copied from [monthlyTransferAmount].
class MonthlyTransferAmountProvider extends AutoDisposeFutureProvider<double> {
  /// Monthly transfer amount provider
  ///
  /// Copied from [monthlyTransferAmount].
  MonthlyTransferAmountProvider(
    DateTime month,
    String accountId,
  ) : this._internal(
          (ref) => monthlyTransferAmount(
            ref as MonthlyTransferAmountRef,
            month,
            accountId,
          ),
          from: monthlyTransferAmountProvider,
          name: r'monthlyTransferAmountProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$monthlyTransferAmountHash,
          dependencies: MonthlyTransferAmountFamily._dependencies,
          allTransitiveDependencies:
              MonthlyTransferAmountFamily._allTransitiveDependencies,
          month: month,
          accountId: accountId,
        );

  MonthlyTransferAmountProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.month,
    required this.accountId,
  }) : super.internal();

  final DateTime month;
  final String accountId;

  @override
  Override overrideWith(
    FutureOr<double> Function(MonthlyTransferAmountRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: MonthlyTransferAmountProvider._internal(
        (ref) => create(ref as MonthlyTransferAmountRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        month: month,
        accountId: accountId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<double> createElement() {
    return _MonthlyTransferAmountProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is MonthlyTransferAmountProvider &&
        other.month == month &&
        other.accountId == accountId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, month.hashCode);
    hash = _SystemHash.combine(hash, accountId.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin MonthlyTransferAmountRef on AutoDisposeFutureProviderRef<double> {
  /// The parameter `month` of this provider.
  DateTime get month;

  /// The parameter `accountId` of this provider.
  String get accountId;
}

class _MonthlyTransferAmountProviderElement
    extends AutoDisposeFutureProviderElement<double>
    with MonthlyTransferAmountRef {
  _MonthlyTransferAmountProviderElement(super.provider);

  @override
  DateTime get month => (origin as MonthlyTransferAmountProvider).month;
  @override
  String get accountId => (origin as MonthlyTransferAmountProvider).accountId;
}

String _$transfersHash() => r'7f11cce8f86c21b9464f92d400f5085177d62a85';

/// Transfers provider - provides all transfers
///
/// Copied from [Transfers].
@ProviderFor(Transfers)
final transfersProvider =
    AutoDisposeAsyncNotifierProvider<Transfers, List<Transfer>>.internal(
  Transfers.new,
  name: r'transfersProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$transfersHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$Transfers = AutoDisposeAsyncNotifier<List<Transfer>>;
String _$transferValidationHash() =>
    r'3aa4c024c3542488515d43de4637aa0aadf3b999';

/// Transfer validation provider
///
/// Copied from [TransferValidation].
@ProviderFor(TransferValidation)
final transferValidationProvider =
    AutoDisposeNotifierProvider<TransferValidation, String?>.internal(
  TransferValidation.new,
  name: r'transferValidationProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$transferValidationHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$TransferValidation = AutoDisposeNotifier<String?>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
