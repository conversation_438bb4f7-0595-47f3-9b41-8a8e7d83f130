import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../data/database/hive_database_service.dart';
import '../data/repositories/account_repository.dart';
import '../data/repositories/transaction_repository.dart';

/// Database service provider
final databaseServiceProvider = Provider<HiveDatabaseService>((ref) {
  return HiveDatabaseService.instance;
});

/// Account repository provider
final accountRepositoryProvider = Provider<AccountRepository>((ref) {
  final databaseService = ref.watch(databaseServiceProvider);
  return AccountRepository(databaseService);
});

/// Transaction repository provider
final transactionRepositoryProvider = Provider<TransactionRepository>((ref) {
  final databaseService = ref.watch(databaseServiceProvider);
  return TransactionRepository(databaseService);
});
