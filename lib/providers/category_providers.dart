import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../data/models/category.dart';
import '../data/repositories/category_repository.dart';

part 'category_providers.g.dart';

/// Category repository provider
@riverpod
CategoryRepository categoryRepository(CategoryRepositoryRef ref) {
  return CategoryRepository();
}

/// Categories provider - provides all active categories
@riverpod
class Categories extends _$Categories {
  @override
  Future<List<Category>> build() async {
    final repository = ref.read(categoryRepositoryProvider);
    await repository.initialize();
    return repository.getAllCategories();
  }

  /// Create a new category
  Future<Category> createCategory({
    required String name,
    required String icon,
    required String color,
    List<String> keywords = const [],
    double? monthlyBudget,
  }) async {
    final repository = ref.read(categoryRepositoryProvider);
    
    final category = Category(
      id: 'category_${DateTime.now().millisecondsSinceEpoch}',
      name: name,
      icon: icon,
      color: color,
      keywords: keywords,
      monthlyBudget: monthlyBudget,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
    
    final createdCategory = await repository.createCategory(category);
    
    // Refresh the categories list
    ref.invalidateSelf();
    
    return createdCategory;
  }

  /// Update an existing category
  Future<Category> updateCategory(Category category) async {
    final repository = ref.read(categoryRepositoryProvider);
    
    final updatedCategory = await repository.updateCategory(category);
    
    // Refresh the categories list
    ref.invalidateSelf();
    
    return updatedCategory;
  }

  /// Delete a category
  Future<void> deleteCategory(String id) async {
    final repository = ref.read(categoryRepositoryProvider);
    
    await repository.deleteCategory(id);
    
    // Refresh the categories list
    ref.invalidateSelf();
  }

  /// Refresh categories
  Future<void> refresh() async {
    ref.invalidateSelf();
  }
}

/// Category by ID provider
@riverpod
Future<Category?> categoryById(CategoryByIdRef ref, String id) async {
  final repository = ref.read(categoryRepositoryProvider);
  await repository.initialize();
  return repository.getCategoryById(id);
}

/// Category suggestions provider - suggests categories based on transaction description
@riverpod
Future<List<Category>> categorySuggestions(
  CategorySuggestionsRef ref, 
  String description,
) async {
  if (description.isEmpty) {
    return [];
  }
  
  final repository = ref.read(categoryRepositoryProvider);
  await repository.initialize();
  return repository.getCategoriesForDescription(description);
}

/// Categories with budgets provider
@riverpod
Future<List<Category>> categoriesWithBudgets(CategoryWithBudgetsRef ref) async {
  final repository = ref.read(categoryRepositoryProvider);
  await repository.initialize();
  return repository.getCategoriesWithBudgets();
}

/// Category search provider
@riverpod
Future<List<Category>> categorySearch(
  CategorySearchRef ref, 
  String query,
) async {
  final repository = ref.read(categoryRepositoryProvider);
  await repository.initialize();
  return repository.searchCategories(query);
}

/// Default categories provider
@riverpod
List<Category> defaultCategories(DefaultCategoriesRef ref) {
  return DefaultCategories.createDefaultCategories();
}

/// Category icons provider
@riverpod
List<String> categoryIcons(CategoryIconsRef ref) {
  return CategoryIcons.availableIcons;
}

/// Category colors provider
@riverpod
List<String> categoryColors(CategoryColorsRef ref) {
  return CategoryColors.availableColors;
}

/// Smart category assignment provider
@riverpod
class SmartCategoryAssignment extends _$SmartCategoryAssignment {
  @override
  String? build() {
    return null; // Initial state
  }

  /// Get smart category suggestion for a transaction description
  Future<String?> getSuggestion(String description) async {
    if (description.isEmpty) {
      return null;
    }

    final suggestions = await ref.read(categorySuggestionsProvider(description).future);
    
    if (suggestions.isNotEmpty) {
      // Return the best match (first in the list)
      final suggestion = suggestions.first.id;
      state = suggestion;
      return suggestion;
    }
    
    // If no suggestions found, return "other" category
    final defaultCategories = ref.read(defaultCategoriesProvider);
    final otherCategory = defaultCategories.firstWhere(
      (c) => c.id == 'other',
      orElse: () => defaultCategories.first,
    );
    
    state = otherCategory.id;
    return otherCategory.id;
  }

  /// Learn from user's category selection
  Future<void> learnFromSelection(String description, String categoryId) async {
    // This could be enhanced to store user preferences and improve suggestions
    // For now, we'll just update the state
    state = categoryId;
    
    // TODO: Implement machine learning logic to improve suggestions
    // This could involve:
    // 1. Storing user's category choices for specific descriptions
    // 2. Building a local database of description -> category mappings
    // 3. Using frequency analysis to improve suggestions
  }

  /// Clear the current suggestion
  void clearSuggestion() {
    state = null;
  }
}

/// Category usage statistics provider
@riverpod
class CategoryUsageStats extends _$CategoryUsageStats {
  @override
  Future<Map<String, int>> build() async {
    // This would typically come from transaction data
    // For now, return empty map
    return {};
  }

  /// Update usage statistics when a category is used
  Future<void> updateUsage(String categoryId) async {
    final currentStats = await future;
    final newStats = Map<String, int>.from(currentStats);
    newStats[categoryId] = (newStats[categoryId] ?? 0) + 1;
    state = AsyncValue.data(newStats);
  }

  /// Get most used categories
  Future<List<String>> getMostUsedCategories({int limit = 5}) async {
    final stats = await future;
    final sortedEntries = stats.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return sortedEntries.take(limit).map((e) => e.key).toList();
  }
}
