import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../data/models/transaction.dart';
import '../data/models/category.dart';
import '../data/models/transfer.dart';
import 'transaction_providers.dart';
import 'category_providers.dart';
import 'transfer_providers.dart';

part 'analytics_providers.g.dart';

/// Monthly spending analytics
@riverpod
class MonthlySpendingAnalytics extends _$MonthlySpendingAnalytics {
  @override
  Future<Map<String, dynamic>> build(DateTime month) async {
    final transactions = await ref.watch(transactionsProvider.future);
    final categories = await ref.watch(categoriesProvider.future);
    
    // Filter transactions for the specified month
    final monthTransactions = transactions.where((transaction) {
      return transaction.date.year == month.year &&
             transaction.date.month == month.month;
    }).toList();
    
    // Calculate totals
    double totalIncome = 0;
    double totalExpenses = 0;
    Map<String, double> categorySpending = {};
    Map<String, int> categoryCount = {};
    
    for (final transaction in monthTransactions) {
      if (transaction.type.isIncome) {
        totalIncome += transaction.amount;
      } else {
        totalExpenses += transaction.amount;
        
        // Category breakdown for expenses
        final categoryId = transaction.categoryId ?? 'other';
        categorySpending[categoryId] = (categorySpending[categoryId] ?? 0) + transaction.amount;
        categoryCount[categoryId] = (categoryCount[categoryId] ?? 0) + 1;
      }
    }
    
    // Get category details
    final categoryBreakdown = <Map<String, dynamic>>[];
    for (final entry in categorySpending.entries) {
      final category = categories.firstWhere(
        (c) => c.id == entry.key,
        orElse: () => Category(
          id: 'unknown',
          name: 'Unknown',
          icon: '❓',
          color: '#757575',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      );
      
      categoryBreakdown.add({
        'categoryId': entry.key,
        'categoryName': category.name,
        'categoryIcon': category.icon,
        'categoryColor': category.color,
        'amount': entry.value,
        'count': categoryCount[entry.key] ?? 0,
        'percentage': totalExpenses > 0 ? (entry.value / totalExpenses) * 100 : 0,
      });
    }
    
    // Sort by amount (highest first)
    categoryBreakdown.sort((a, b) => (b['amount'] as double).compareTo(a['amount'] as double));
    
    return {
      'totalIncome': totalIncome,
      'totalExpenses': totalExpenses,
      'netAmount': totalIncome - totalExpenses,
      'transactionCount': monthTransactions.length,
      'categoryBreakdown': categoryBreakdown,
      'topCategories': categoryBreakdown.take(5).toList(),
      'averageTransactionAmount': monthTransactions.isNotEmpty 
          ? (totalIncome + totalExpenses) / monthTransactions.length 
          : 0,
    };
  }
}

/// Weekly spending trend
@riverpod
Future<List<Map<String, dynamic>>> weeklySpendingTrend(
  WeeklySpendingTrendRef ref,
  DateTime startDate,
) async {
  final transactions = await ref.watch(transactionsProvider.future);
  
  final weeklyData = <Map<String, dynamic>>[];
  
  for (int i = 0; i < 7; i++) {
    final date = startDate.add(Duration(days: i));
    final dayTransactions = transactions.where((transaction) {
      return transaction.date.year == date.year &&
             transaction.date.month == date.month &&
             transaction.date.day == date.day;
    }).toList();
    
    double dayIncome = 0;
    double dayExpenses = 0;
    
    for (final transaction in dayTransactions) {
      if (transaction.type.isIncome) {
        dayIncome += transaction.amount;
      } else {
        dayExpenses += transaction.amount;
      }
    }
    
    weeklyData.add({
      'date': date,
      'dayName': _getDayName(date.weekday),
      'income': dayIncome,
      'expenses': dayExpenses,
      'net': dayIncome - dayExpenses,
      'transactionCount': dayTransactions.length,
    });
  }
  
  return weeklyData;
}

/// Account balance distribution
@riverpod
Future<List<Map<String, dynamic>>> accountBalanceDistribution(
  AccountBalanceDistributionRef ref,
) async {
  final accounts = await ref.watch(accountsProvider.future);
  final distribution = <Map<String, dynamic>>[];
  
  double totalBalance = 0;
  final accountBalances = <String, double>{};
  
  // Calculate balances
  for (final account in accounts) {
    final balance = await ref.watch(accountBalanceProvider(account.id).future);
    accountBalances[account.id] = balance;
    totalBalance += balance;
  }
  
  // Create distribution data
  for (final account in accounts) {
    final balance = accountBalances[account.id] ?? 0;
    distribution.add({
      'accountId': account.id,
      'accountName': account.name,
      'accountType': account.type.displayName,
      'accountIcon': account.type.icon,
      'balance': balance,
      'percentage': totalBalance > 0 ? (balance / totalBalance) * 100 : 0,
      'isPositive': balance >= 0,
    });
  }
  
  // Sort by balance (highest first)
  distribution.sort((a, b) => (b['balance'] as double).compareTo(a['balance'] as double));
  
  return distribution;
}

/// Spending insights and patterns
@riverpod
Future<Map<String, dynamic>> spendingInsights(SpendingInsightsRef ref) async {
  final currentMonth = DateTime.now();
  final lastMonth = DateTime(currentMonth.year, currentMonth.month - 1);
  
  final currentMonthData = await ref.watch(monthlySpendingAnalyticsProvider(currentMonth).future);
  final lastMonthData = await ref.watch(monthlySpendingAnalyticsProvider(lastMonth).future);
  
  final currentExpenses = currentMonthData['totalExpenses'] as double;
  final lastMonthExpenses = lastMonthData['totalExpenses'] as double;
  
  // Calculate trends
  final expenseChange = lastMonthExpenses > 0 
      ? ((currentExpenses - lastMonthExpenses) / lastMonthExpenses) * 100
      : 0.0;
  
  final insights = <String>[];
  
  // Generate insights
  if (expenseChange > 20) {
    insights.add('Your spending increased by ${expenseChange.toStringAsFixed(1)}% this month');
  } else if (expenseChange < -20) {
    insights.add('Great job! You reduced spending by ${(-expenseChange).toStringAsFixed(1)}% this month');
  }
  
  // Top category insight
  final topCategories = currentMonthData['topCategories'] as List;
  if (topCategories.isNotEmpty) {
    final topCategory = topCategories.first;
    insights.add('${topCategory['categoryName']} is your biggest expense category');
  }
  
  // Budget insights (if categories have budgets)
  final categories = await ref.watch(categoriesWithBudgetsProvider.future);
  for (final category in categories) {
    final categoryData = (currentMonthData['categoryBreakdown'] as List)
        .firstWhere(
          (c) => c['categoryId'] == category.id,
          orElse: () => {'amount': 0.0},
        );
    
    final spent = categoryData['amount'] as double;
    final budget = category.monthlyBudget ?? 0;
    
    if (budget > 0) {
      final percentage = (spent / budget) * 100;
      if (percentage > 100) {
        insights.add('You exceeded your ${category.name} budget by ${(percentage - 100).toStringAsFixed(1)}%');
      } else if (percentage > 80) {
        insights.add('You\'re at ${percentage.toStringAsFixed(1)}% of your ${category.name} budget');
      }
    }
  }
  
  return {
    'currentMonthExpenses': currentExpenses,
    'lastMonthExpenses': lastMonthExpenses,
    'expenseChange': expenseChange,
    'expenseChangeDirection': expenseChange > 0 ? 'increase' : 'decrease',
    'insights': insights,
    'topSpendingCategory': topCategories.isNotEmpty ? topCategories.first : null,
  };
}

/// Transfer analytics
@riverpod
Future<Map<String, dynamic>> transferAnalytics(
  TransferAnalyticsRef ref,
  DateTime month,
) async {
  final transfers = await ref.watch(transfersProvider.future);
  
  // Filter transfers for the specified month
  final monthTransfers = transfers.where((transfer) {
    return transfer.date.year == month.year &&
           transfer.date.month == month.month;
  }).toList();
  
  double totalTransferAmount = 0;
  Map<String, int> transferRoutes = {};
  
  for (final transfer in monthTransfers) {
    totalTransferAmount += transfer.amount;
    
    // Track transfer routes
    final route = '${transfer.fromAccountId}->${transfer.toAccountId}';
    transferRoutes[route] = (transferRoutes[route] ?? 0) + 1;
  }
  
  // Find most common transfer route
  String? mostCommonRoute;
  int maxRouteCount = 0;
  transferRoutes.forEach((route, count) {
    if (count > maxRouteCount) {
      maxRouteCount = count;
      mostCommonRoute = route;
    }
  });
  
  return {
    'totalTransfers': monthTransfers.length,
    'totalTransferAmount': totalTransferAmount,
    'averageTransferAmount': monthTransfers.isNotEmpty 
        ? totalTransferAmount / monthTransfers.length 
        : 0,
    'mostCommonRoute': mostCommonRoute,
    'mostCommonRouteCount': maxRouteCount,
    'transferRoutes': transferRoutes,
  };
}

/// Chart data for spending by category (pie chart)
@riverpod
Future<List<Map<String, dynamic>>> categorySpendingChartData(
  CategorySpendingChartDataRef ref,
  DateTime month,
) async {
  final analytics = await ref.watch(monthlySpendingAnalyticsProvider(month).future);
  final categoryBreakdown = analytics['categoryBreakdown'] as List<Map<String, dynamic>>;
  
  return categoryBreakdown.map((category) => {
    'name': category['categoryName'],
    'value': category['amount'],
    'percentage': category['percentage'],
    'color': category['categoryColor'],
    'icon': category['categoryIcon'],
  }).toList();
}

/// Chart data for weekly spending trend (line chart)
@riverpod
Future<List<Map<String, dynamic>>> weeklySpendingChartData(
  WeeklySpendingChartDataRef ref,
  DateTime startDate,
) async {
  final weeklyData = await ref.watch(weeklySpendingTrendProvider(startDate).future);
  
  return weeklyData.map((day) => {
    'day': day['dayName'],
    'date': day['date'],
    'expenses': day['expenses'],
    'income': day['income'],
    'net': day['net'],
  }).toList();
}

/// Helper function to get day name
String _getDayName(int weekday) {
  switch (weekday) {
    case 1: return 'Mon';
    case 2: return 'Tue';
    case 3: return 'Wed';
    case 4: return 'Thu';
    case 5: return 'Fri';
    case 6: return 'Sat';
    case 7: return 'Sun';
    default: return '';
  }
}
