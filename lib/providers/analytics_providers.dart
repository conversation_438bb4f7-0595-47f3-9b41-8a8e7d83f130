import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart' hide Category;
import '../data/models/transaction.dart';
import '../data/models/category.dart';
import '../core/error/error_handler.dart';
import 'transaction_providers.dart';
import 'category_providers.dart';
import 'account_providers.dart';

part 'analytics_providers.g.dart';

/// Monthly spending analytics
@riverpod
class MonthlySpendingAnalytics extends _$MonthlySpendingAnalytics {
  @override
  Future<Map<String, dynamic>> build(DateTime month) async {
    try {
      final transactions = await ref.watch(transactionsProvider.future);
      final categories = await ref.watch(categoriesProvider.future);

      // Filter transactions for the specified month
      final monthTransactions = transactions.where((transaction) {
        return transaction.date.year == month.year &&
               transaction.date.month == month.month;
      }).toList();

      // Calculate totals
      double totalIncome = 0;
      double totalExpenses = 0;
      Map<String, double> categorySpending = {};
      Map<String, int> categoryCount = {};

      for (final transaction in monthTransactions) {
        if (transaction.type.isIncome) {
          totalIncome += transaction.amount;
        } else {
          totalExpenses += transaction.amount;

          // Category breakdown for expenses
          final categoryId = transaction.categoryId ?? 'other';
          categorySpending[categoryId] = (categorySpending[categoryId] ?? 0) + transaction.amount;
          categoryCount[categoryId] = (categoryCount[categoryId] ?? 0) + 1;
        }
      }

      // Get category details
      final categoryBreakdown = <Map<String, dynamic>>[];
      for (final entry in categorySpending.entries) {
        Category? category;
        try {
          category = categories.firstWhere((c) => c.id == entry.key);
        } catch (e) {
          category = Category(
            id: 'unknown',
            name: 'Unknown',
            icon: '❓',
            color: '#757575',
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );
        }

        categoryBreakdown.add({
          'categoryId': entry.key,
          'categoryName': category.name,
          'categoryIcon': category.icon,
          'categoryColor': category.color,
          'amount': entry.value,
          'count': categoryCount[entry.key] ?? 0,
          'percentage': totalExpenses > 0 ? (entry.value / totalExpenses) * 100 : 0,
        });
      }

      // Sort by amount (highest first)
      categoryBreakdown.sort((a, b) => (b['amount'] as double).compareTo(a['amount'] as double));

      return {
        'totalIncome': totalIncome,
        'totalExpenses': totalExpenses,
        'netAmount': totalIncome - totalExpenses,
        'transactionCount': monthTransactions.length,
        'categoryBreakdown': categoryBreakdown,
        'topCategories': categoryBreakdown.take(5).toList(),
        'averageTransactionAmount': monthTransactions.isNotEmpty
            ? (totalIncome + totalExpenses) / monthTransactions.length
            : 0,
      };
    } catch (error, stackTrace) {
      debugPrint('Error loading monthly spending analytics: $error');
      debugPrint('Stack trace: $stackTrace');
      final userMessage = ErrorHandler.handleError(error, context: 'Monthly Analytics');
      throw Exception(userMessage);
    }
  }
}

/// Weekly spending trend
@riverpod
Future<List<Map<String, dynamic>>> weeklySpendingTrend(
  Ref ref,
  DateTime startDate,
) async {
  try {
    final transactions = await ref.watch(transactionsProvider.future);

    final weeklyData = <Map<String, dynamic>>[];

    for (int week = 0; week < 4; week++) {
      final weekStart = startDate.add(Duration(days: week * 7));
      final weekEnd = weekStart.add(const Duration(days: 6));

      final weekTransactions = transactions.where((transaction) {
        return transaction.date.isAfter(weekStart.subtract(const Duration(days: 1))) &&
               transaction.date.isBefore(weekEnd.add(const Duration(days: 1))) &&
               transaction.type.isExpense;
      }).toList();

      final totalSpending = weekTransactions.fold<double>(
        0, (sum, transaction) => sum + transaction.amount,
      );

      weeklyData.add({
        'week': week + 1,
        'weekStart': weekStart,
        'weekEnd': weekEnd,
        'totalSpending': totalSpending,
        'transactionCount': weekTransactions.length,
        'averageDaily': totalSpending / 7,
      });
    }

    return weeklyData;
  } catch (error, stackTrace) {
    debugPrint('Error loading weekly spending trend: $error');
    debugPrint('Stack trace: $stackTrace');
    final userMessage = ErrorHandler.handleError(error, context: 'Weekly Trend');
    throw Exception(userMessage);
  }
}

/// Account balance distribution
@riverpod
Future<List<Map<String, dynamic>>> accountBalanceDistribution(
  Ref ref,
) async {
  try {
    final accounts = await ref.watch(accountsProvider.future);
    final distribution = <Map<String, dynamic>>[];

    double totalBalance = 0;
    final accountBalances = <String, double>{};

    // Calculate balances
    for (final account in accounts) {
      final balance = await ref.watch(accountBalanceProvider(account.id).future);
      accountBalances[account.id] = balance;
      totalBalance += balance;
    }

    // Create distribution data
    for (final account in accounts) {
      final balance = accountBalances[account.id] ?? 0;
      distribution.add({
        'accountId': account.id,
        'accountName': account.name,
        'accountType': account.type.name,
        'accountIcon': account.type.name == 'cash' ? '💵' : '🏦',
        'balance': balance,
        'percentage': totalBalance > 0 ? (balance / totalBalance) * 100 : 0,
        'isPositive': balance >= 0,
      });
    }

    // Sort by balance (highest first)
    distribution.sort((a, b) => (b['balance'] as double).compareTo(a['balance'] as double));

    return distribution;
  } catch (error, stackTrace) {
    debugPrint('Error loading account balance distribution: $error');
    debugPrint('Stack trace: $stackTrace');
    final userMessage = ErrorHandler.handleError(error, context: 'Account Distribution');
    throw Exception(userMessage);
  }
}

/// Spending insights and patterns
@riverpod
Future<Map<String, dynamic>> spendingInsights(Ref ref) async {
  try {
    final currentMonth = DateTime.now();
    final lastMonth = DateTime(currentMonth.year, currentMonth.month - 1);

    final currentMonthAnalytics = await ref.watch(monthlySpendingAnalyticsProvider(currentMonth).future);
    final lastMonthAnalytics = await ref.watch(monthlySpendingAnalyticsProvider(lastMonth).future);

    final currentExpenses = currentMonthAnalytics['totalExpenses'] as double;
    final lastExpenses = lastMonthAnalytics['totalExpenses'] as double;

    final expenseChange = lastExpenses > 0
        ? ((currentExpenses - lastExpenses) / lastExpenses) * 100
        : 0.0;

    final insights = <String>[];

    if (expenseChange > 10) {
      insights.add('Your spending increased by ${expenseChange.toStringAsFixed(1)}% this month');
    } else if (expenseChange < -10) {
      insights.add('Great job! You reduced spending by ${(-expenseChange).toStringAsFixed(1)}% this month');
    } else {
      insights.add('Your spending is consistent with last month');
    }

    // Top category insight
    final topCategories = currentMonthAnalytics['topCategories'] as List;
    if (topCategories.isNotEmpty) {
      final topCategory = topCategories.first;
      insights.add('${topCategory['categoryName']} is your biggest expense category');
    }

    return {
      'currentMonthExpenses': currentExpenses,
      'lastMonthExpenses': lastExpenses,
      'expenseChange': expenseChange,
      'insights': insights,
      'topCategory': topCategories.isNotEmpty ? topCategories.first : null,
    };
  } catch (error, stackTrace) {
    debugPrint('Error loading spending insights: $error');
    debugPrint('Stack trace: $stackTrace');
    final userMessage = ErrorHandler.handleError(error, context: 'Spending Insights');
    throw Exception(userMessage);
  }
}

/// Chart data for spending by category (pie chart)
@riverpod
Future<List<Map<String, dynamic>>> categorySpendingChartData(
  Ref ref,
  DateTime month,
) async {
  try {
    final analytics = await ref.watch(monthlySpendingAnalyticsProvider(month).future);
    final categoryBreakdown = analytics['categoryBreakdown'] as List<Map<String, dynamic>>;

    return categoryBreakdown.map((category) => {
      'name': category['categoryName'],
      'value': category['amount'],
      'percentage': category['percentage'],
      'color': category['categoryColor'],
      'icon': category['categoryIcon'],
    }).toList();
  } catch (error, stackTrace) {
    debugPrint('Error loading category spending chart data: $error');
    debugPrint('Stack trace: $stackTrace');
    final userMessage = ErrorHandler.handleError(error, context: 'Category Chart');
    throw Exception(userMessage);
  }
}

/// Chart data for weekly spending trend (line chart)
@riverpod
Future<List<Map<String, dynamic>>> weeklySpendingChartData(
  Ref ref,
  DateTime startDate,
) async {
  try {
    final weeklyData = await ref.watch(weeklySpendingTrendProvider(startDate).future);

    return weeklyData.map((week) => {
      'week': week['week'],
      'amount': week['totalSpending'],
      'date': week['weekStart'],
      'label': 'Week ${week['week']}',
    }).toList();
  } catch (error, stackTrace) {
    debugPrint('Error loading weekly spending chart data: $error');
    debugPrint('Stack trace: $stackTrace');
    final userMessage = ErrorHandler.handleError(error, context: 'Weekly Chart');
    throw Exception(userMessage);
  }
}
