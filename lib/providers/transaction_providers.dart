import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../data/models/transaction.dart';
import '../data/repositories/transaction_repository.dart';
import '../core/constants/app_constants.dart';
import 'database_providers.dart';

/// Provider for all transactions
final transactionsProvider = FutureProvider<List<Transaction>>((ref) async {
  final repository = ref.watch(transactionRepositoryProvider);
  return repository.getTransactions();
});

/// Provider for recent transactions
final recentTransactionsProvider = FutureProvider<List<Transaction>>((ref) async {
  final repository = ref.watch(transactionRepositoryProvider);
  return repository.getRecentTransactions(AppConstants.recentTransactionsCount);
});

/// Provider for transactions by account
final transactionsByAccountProvider = FutureProvider.family<List<Transaction>, String>((ref, accountId) async {
  final repository = ref.watch(transactionRepositoryProvider);
  return repository.getTransactions(accountId: accountId);
});

/// Provider for a specific transaction
final transactionProvider = FutureProvider.family<Transaction?, String>((ref, transactionId) async {
  final repository = ref.watch(transactionRepositoryProvider);
  return repository.getTransaction(transactionId);
});

/// Provider for grouped transactions
final groupedTransactionsProvider = FutureProvider<Map<String, List<Transaction>>>((ref) async {
  final repository = ref.watch(transactionRepositoryProvider);
  return repository.getTransactionsGroupedByDate();
});

/// Provider for recent descriptions
final recentDescriptionsProvider = FutureProvider<List<String>>((ref) async {
  final repository = ref.watch(transactionRepositoryProvider);
  return repository.getRecentDescriptions();
});

/// Provider for transaction statistics
final transactionStatisticsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final repository = ref.watch(transactionRepositoryProvider);
  return repository.getTransactionStatistics();
});

/// Notifier for managing transaction state
class TransactionNotifier extends StateNotifier<AsyncValue<List<Transaction>>> {
  TransactionNotifier(this._repository) : super(const AsyncValue.loading()) {
    _loadTransactions();
  }

  final TransactionRepository _repository;

  Future<void> _loadTransactions() async {
    try {
      final transactions = await _repository.getTransactions();
      state = AsyncValue.data(transactions);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  Future<void> createTransaction({
    required String accountId,
    required TransactionType type,
    required double amount,
    required String description,
    DateTime? date,
    String? note,
  }) async {
    try {
      await _repository.createTransaction(
        accountId: accountId,
        type: type,
        amount: amount,
        description: description,
        date: date,
        note: note,
      );
      await _loadTransactions(); // Refresh the list
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
      rethrow;
    }
  }

  Future<void> createIncome({
    required String accountId,
    required double amount,
    required String description,
    DateTime? date,
    String? note,
  }) async {
    return createTransaction(
      accountId: accountId,
      type: TransactionType.income,
      amount: amount,
      description: description,
      date: date,
      note: note,
    );
  }

  Future<void> createExpense({
    required String accountId,
    required double amount,
    required String description,
    DateTime? date,
    String? note,
  }) async {
    return createTransaction(
      accountId: accountId,
      type: TransactionType.expense,
      amount: amount,
      description: description,
      date: date,
      note: note,
    );
  }

  Future<void> updateTransaction(Transaction transaction) async {
    try {
      await _repository.updateTransaction(transaction);
      await _loadTransactions(); // Refresh the list
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
      rethrow;
    }
  }

  Future<void> deleteTransaction(String transactionId) async {
    try {
      await _repository.deleteTransaction(transactionId);
      await _loadTransactions(); // Refresh the list
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
      rethrow;
    }
  }

  void refresh() {
    _loadTransactions();
  }
}

/// Provider for transaction notifier
final transactionNotifierProvider = StateNotifierProvider<TransactionNotifier, AsyncValue<List<Transaction>>>((ref) {
  final repository = ref.watch(transactionRepositoryProvider);
  return TransactionNotifier(repository);
});

/// Notifier for managing recent transactions state
class RecentTransactionNotifier extends StateNotifier<AsyncValue<List<Transaction>>> {
  RecentTransactionNotifier(this._repository) : super(const AsyncValue.loading()) {
    _loadRecentTransactions();
  }

  final TransactionRepository _repository;

  Future<void> _loadRecentTransactions() async {
    try {
      final transactions = await _repository.getRecentTransactions(AppConstants.recentTransactionsCount);
      state = AsyncValue.data(transactions);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  void refresh() {
    _loadRecentTransactions();
  }
}

/// Provider for recent transaction notifier
final recentTransactionNotifierProvider = StateNotifierProvider<RecentTransactionNotifier, AsyncValue<List<Transaction>>>((ref) {
  final repository = ref.watch(transactionRepositoryProvider);
  return RecentTransactionNotifier(repository);
});

/// Search notifier for transactions
class TransactionSearchNotifier extends StateNotifier<AsyncValue<List<Transaction>>> {
  TransactionSearchNotifier(this._repository) : super(const AsyncValue.data([]));

  final TransactionRepository _repository;
  String _currentQuery = '';

  String get currentQuery => _currentQuery;

  Future<void> search(String query) async {
    if (query == _currentQuery) return;

    _currentQuery = query;

    if (query.trim().isEmpty) {
      state = const AsyncValue.data([]);
      return;
    }

    state = const AsyncValue.loading();

    try {
      final transactions = await _repository.searchTransactions(query);
      state = AsyncValue.data(transactions);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  void clear() {
    _currentQuery = '';
    state = const AsyncValue.data([]);
  }
}

/// Provider for transaction search notifier
final transactionSearchNotifierProvider = StateNotifierProvider<TransactionSearchNotifier, AsyncValue<List<Transaction>>>((ref) {
  final repository = ref.watch(transactionRepositoryProvider);
  return TransactionSearchNotifier(repository);
});
