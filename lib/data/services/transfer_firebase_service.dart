import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/transfer.dart';

class TransferFirebaseService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Get the current user's transfers collection reference
  CollectionReference<Map<String, dynamic>>? get _transfersCollection {
    final user = _auth.currentUser;
    if (user == null) return null;
    
    return _firestore
        .collection('users')
        .doc(user.uid)
        .collection('transfers');
  }

  /// Sync a transfer to Firebase
  Future<String?> syncTransfer(Transfer transfer) async {
    try {
      final collection = _transfersCollection;
      if (collection == null) {
        throw Exception('User not authenticated');
      }

      final transferData = transfer.toFirebaseMap();
      
      if (transfer.firebaseId != null) {
        // Update existing transfer
        await collection.doc(transfer.firebaseId).update(transferData);
        return transfer.firebaseId;
      } else {
        // Create new transfer
        final docRef = await collection.add(transferData);
        return docRef.id;
      }
    } catch (e) {
      throw Exception('Failed to sync transfer: $e');
    }
  }

  /// Fetch all transfers from Firebase
  Future<List<Transfer>> fetchTransfers() async {
    try {
      final collection = _transfersCollection;
      if (collection == null) {
        throw Exception('User not authenticated');
      }

      final querySnapshot = await collection
          .where('isDeleted', isEqualTo: false)
          .orderBy('date', descending: true)
          .get();

      return querySnapshot.docs.map((doc) {
        return Transfer.fromFirebaseMap(doc.data(), doc.id);
      }).toList();
    } catch (e) {
      throw Exception('Failed to fetch transfers: $e');
    }
  }

  /// Fetch transfers for a specific account
  Future<List<Transfer>> fetchTransfersForAccount(String accountId) async {
    try {
      final collection = _transfersCollection;
      if (collection == null) {
        throw Exception('User not authenticated');
      }

      // Query for transfers where the account is either source or destination
      final fromQuery = collection
          .where('isDeleted', isEqualTo: false)
          .where('fromAccountId', isEqualTo: accountId)
          .orderBy('date', descending: true);

      final toQuery = collection
          .where('isDeleted', isEqualTo: false)
          .where('toAccountId', isEqualTo: accountId)
          .orderBy('date', descending: true);

      final fromResults = await fromQuery.get();
      final toResults = await toQuery.get();

      final allTransfers = <Transfer>[];
      
      // Add transfers from both queries
      allTransfers.addAll(fromResults.docs.map((doc) {
        return Transfer.fromFirebaseMap(doc.data(), doc.id);
      }));
      
      allTransfers.addAll(toResults.docs.map((doc) {
        return Transfer.fromFirebaseMap(doc.data(), doc.id);
      }));

      // Remove duplicates and sort by date
      final uniqueTransfers = allTransfers.toSet().toList();
      uniqueTransfers.sort((a, b) => b.date.compareTo(a.date));

      return uniqueTransfers;
    } catch (e) {
      throw Exception('Failed to fetch transfers for account: $e');
    }
  }

  /// Fetch transfers between two accounts
  Future<List<Transfer>> fetchTransfersBetweenAccounts(
    String fromAccountId, 
    String toAccountId,
  ) async {
    try {
      final collection = _transfersCollection;
      if (collection == null) {
        throw Exception('User not authenticated');
      }

      final querySnapshot = await collection
          .where('isDeleted', isEqualTo: false)
          .where('fromAccountId', isEqualTo: fromAccountId)
          .where('toAccountId', isEqualTo: toAccountId)
          .orderBy('date', descending: true)
          .get();

      return querySnapshot.docs.map((doc) {
        return Transfer.fromFirebaseMap(doc.data(), doc.id);
      }).toList();
    } catch (e) {
      throw Exception('Failed to fetch transfers between accounts: $e');
    }
  }

  /// Fetch transfers within a date range
  Future<List<Transfer>> fetchTransfersInDateRange(
    DateTime startDate, 
    DateTime endDate,
  ) async {
    try {
      final collection = _transfersCollection;
      if (collection == null) {
        throw Exception('User not authenticated');
      }

      final querySnapshot = await collection
          .where('isDeleted', isEqualTo: false)
          .where('date', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate))
          .where('date', isLessThanOrEqualTo: Timestamp.fromDate(endDate))
          .orderBy('date', descending: true)
          .get();

      return querySnapshot.docs.map((doc) {
        return Transfer.fromFirebaseMap(doc.data(), doc.id);
      }).toList();
    } catch (e) {
      throw Exception('Failed to fetch transfers in date range: $e');
    }
  }

  /// Fetch transfers updated after a specific timestamp
  Future<List<Transfer>> fetchTransfersUpdatedAfter(DateTime timestamp) async {
    try {
      final collection = _transfersCollection;
      if (collection == null) {
        throw Exception('User not authenticated');
      }

      final querySnapshot = await collection
          .where('updatedAt', isGreaterThan: Timestamp.fromDate(timestamp))
          .orderBy('updatedAt')
          .get();

      return querySnapshot.docs.map((doc) {
        return Transfer.fromFirebaseMap(doc.data(), doc.id);
      }).toList();
    } catch (e) {
      throw Exception('Failed to fetch updated transfers: $e');
    }
  }

  /// Delete a transfer from Firebase
  Future<void> deleteTransfer(String firebaseId) async {
    try {
      final collection = _transfersCollection;
      if (collection == null) {
        throw Exception('User not authenticated');
      }

      await collection.doc(firebaseId).update({
        'isDeleted': true,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw Exception('Failed to delete transfer: $e');
    }
  }

  /// Listen to real-time transfer changes
  Stream<List<Transfer>> listenToTransfers() {
    final collection = _transfersCollection;
    if (collection == null) {
      return Stream.error('User not authenticated');
    }

    return collection
        .where('isDeleted', isEqualTo: false)
        .orderBy('date', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        return Transfer.fromFirebaseMap(doc.data(), doc.id);
      }).toList();
    });
  }

  /// Search transfers by description
  Future<List<Transfer>> searchTransfersByDescription(String query) async {
    try {
      final collection = _transfersCollection;
      if (collection == null) {
        throw Exception('User not authenticated');
      }

      final querySnapshot = await collection
          .where('isDeleted', isEqualTo: false)
          .where('description', isGreaterThanOrEqualTo: query)
          .where('description', isLessThan: query + '\uf8ff')
          .orderBy('description')
          .orderBy('date', descending: true)
          .get();

      return querySnapshot.docs.map((doc) {
        return Transfer.fromFirebaseMap(doc.data(), doc.id);
      }).toList();
    } catch (e) {
      throw Exception('Failed to search transfers: $e');
    }
  }

  /// Batch sync multiple transfers
  Future<void> batchSyncTransfers(List<Transfer> transfers) async {
    try {
      final collection = _transfersCollection;
      if (collection == null) {
        throw Exception('User not authenticated');
      }

      final batch = _firestore.batch();

      for (final transfer in transfers) {
        final transferData = transfer.toFirebaseMap();
        
        if (transfer.firebaseId != null) {
          // Update existing transfer
          batch.update(collection.doc(transfer.firebaseId), transferData);
        } else {
          // Create new transfer
          final docRef = collection.doc();
          batch.set(docRef, transferData);
        }
      }

      await batch.commit();
    } catch (e) {
      throw Exception('Failed to batch sync transfers: $e');
    }
  }
}

/// Extension methods for Transfer Firebase conversion
extension TransferFirebaseExtension on Transfer {
  /// Convert to Firebase document
  Map<String, dynamic> toFirebaseMap() {
    return {
      'id': id,
      'amount': amount,
      'fromAccountId': fromAccountId,
      'toAccountId': toAccountId,
      'description': description,
      'date': Timestamp.fromDate(date),
      'createdAt': Timestamp.fromDate(createdAt),
      'isDeleted': isDeleted,
    };
  }

  /// Create from Firebase document
  static Transfer fromFirebaseMap(Map<String, dynamic> map, String firebaseId) {
    return Transfer(
      id: map['id'] as String,
      amount: (map['amount'] as num).toDouble(),
      fromAccountId: map['fromAccountId'] as String,
      toAccountId: map['toAccountId'] as String,
      description: map['description'] as String?,
      date: (map['date'] as Timestamp).toDate(),
      createdAt: (map['createdAt'] as Timestamp).toDate(),
      isDeleted: map['isDeleted'] as bool? ?? false,
      isSynced: true,
      firebaseId: firebaseId,
    );
  }
}
