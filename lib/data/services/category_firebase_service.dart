import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/category.dart';

class CategoryFirebaseService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Get the current user's categories collection reference
  CollectionReference<Map<String, dynamic>>? get _categoriesCollection {
    final user = _auth.currentUser;
    if (user == null) return null;
    
    return _firestore
        .collection('users')
        .doc(user.uid)
        .collection('categories');
  }

  /// Sync a category to Firebase
  Future<String?> syncCategory(Category category) async {
    try {
      final collection = _categoriesCollection;
      if (collection == null) {
        throw Exception('User not authenticated');
      }

      final categoryData = category.toFirebaseMap();
      
      if (category.firebaseId != null) {
        // Update existing category
        await collection.doc(category.firebaseId).update(categoryData);
        return category.firebaseId;
      } else {
        // Create new category
        final docRef = await collection.add(categoryData);
        return docRef.id;
      }
    } catch (e) {
      throw Exception('Failed to sync category: $e');
    }
  }

  /// Fetch all categories from Firebase
  Future<List<Category>> fetchCategories() async {
    try {
      final collection = _categoriesCollection;
      if (collection == null) {
        throw Exception('User not authenticated');
      }

      final querySnapshot = await collection
          .where('isDeleted', isEqualTo: false)
          .orderBy('name')
          .get();

      return querySnapshot.docs.map((doc) {
        return Category.fromFirebaseMap(doc.data(), doc.id);
      }).toList();
    } catch (e) {
      throw Exception('Failed to fetch categories: $e');
    }
  }

  /// Fetch categories updated after a specific timestamp
  Future<List<Category>> fetchCategoriesUpdatedAfter(DateTime timestamp) async {
    try {
      final collection = _categoriesCollection;
      if (collection == null) {
        throw Exception('User not authenticated');
      }

      final querySnapshot = await collection
          .where('updatedAt', isGreaterThan: Timestamp.fromDate(timestamp))
          .orderBy('updatedAt')
          .get();

      return querySnapshot.docs.map((doc) {
        return Category.fromFirebaseMap(doc.data(), doc.id);
      }).toList();
    } catch (e) {
      throw Exception('Failed to fetch updated categories: $e');
    }
  }

  /// Delete a category from Firebase
  Future<void> deleteCategory(String firebaseId) async {
    try {
      final collection = _categoriesCollection;
      if (collection == null) {
        throw Exception('User not authenticated');
      }

      await collection.doc(firebaseId).update({
        'isDeleted': true,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw Exception('Failed to delete category: $e');
    }
  }

  /// Listen to real-time category changes
  Stream<List<Category>> listenToCategories() {
    final collection = _categoriesCollection;
    if (collection == null) {
      return Stream.error('User not authenticated');
    }

    return collection
        .where('isDeleted', isEqualTo: false)
        .orderBy('name')
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        return Category.fromFirebaseMap(doc.data(), doc.id);
      }).toList();
    });
  }

  /// Search categories by keywords
  Future<List<Category>> searchCategoriesByKeywords(List<String> keywords) async {
    try {
      final collection = _categoriesCollection;
      if (collection == null) {
        throw Exception('User not authenticated');
      }

      final querySnapshot = await collection
          .where('isDeleted', isEqualTo: false)
          .where('keywords', arrayContainsAny: keywords)
          .get();

      return querySnapshot.docs.map((doc) {
        return Category.fromFirebaseMap(doc.data(), doc.id);
      }).toList();
    } catch (e) {
      throw Exception('Failed to search categories: $e');
    }
  }

  /// Get categories with budgets
  Future<List<Category>> fetchCategoriesWithBudgets() async {
    try {
      final collection = _categoriesCollection;
      if (collection == null) {
        throw Exception('User not authenticated');
      }

      final querySnapshot = await collection
          .where('isDeleted', isEqualTo: false)
          .where('monthlyBudget', isGreaterThan: 0)
          .orderBy('monthlyBudget', descending: true)
          .get();

      return querySnapshot.docs.map((doc) {
        return Category.fromFirebaseMap(doc.data(), doc.id);
      }).toList();
    } catch (e) {
      throw Exception('Failed to fetch categories with budgets: $e');
    }
  }

  /// Batch sync multiple categories
  Future<void> batchSyncCategories(List<Category> categories) async {
    try {
      final collection = _categoriesCollection;
      if (collection == null) {
        throw Exception('User not authenticated');
      }

      final batch = _firestore.batch();

      for (final category in categories) {
        final categoryData = category.toFirebaseMap();
        
        if (category.firebaseId != null) {
          // Update existing category
          batch.update(collection.doc(category.firebaseId), categoryData);
        } else {
          // Create new category
          final docRef = collection.doc();
          batch.set(docRef, categoryData);
        }
      }

      await batch.commit();
    } catch (e) {
      throw Exception('Failed to batch sync categories: $e');
    }
  }
}

/// Extension methods for Category Firebase conversion
extension CategoryFirebaseExtension on Category {
  /// Convert to Firebase document
  Map<String, dynamic> toFirebaseMap() {
    return {
      'id': id,
      'name': name,
      'icon': icon,
      'color': color,
      'isDefault': isDefault,
      'keywords': keywords,
      'monthlyBudget': monthlyBudget,
      'isActive': isActive,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'isDeleted': isDeleted,
    };
  }

  /// Create from Firebase document
  static Category fromFirebaseMap(Map<String, dynamic> map, String firebaseId) {
    return Category(
      id: map['id'] as String,
      name: map['name'] as String,
      icon: map['icon'] as String,
      color: map['color'] as String,
      isDefault: map['isDefault'] as bool? ?? false,
      keywords: List<String>.from(map['keywords'] as List? ?? []),
      monthlyBudget: (map['monthlyBudget'] as num?)?.toDouble(),
      isActive: map['isActive'] as bool? ?? true,
      createdAt: (map['createdAt'] as Timestamp).toDate(),
      updatedAt: (map['updatedAt'] as Timestamp).toDate(),
      isDeleted: map['isDeleted'] as bool? ?? false,
      isSynced: true,
      firebaseId: firebaseId,
    );
  }
}
