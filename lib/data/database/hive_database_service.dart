import 'package:flutter/foundation.dart';
import 'package:hive_flutter/hive_flutter.dart';
import '../models/account.dart';
import '../models/transaction.dart';

class HiveDatabaseService {
  static HiveDatabaseService? _instance;
  static HiveDatabaseService get instance {
    _instance ??= HiveDatabaseService._internal();
    return _instance!;
  }

  HiveDatabaseService._internal();

  // Box names
  static const String _accountsBoxName = 'accounts';
  static const String _transactionsBoxName = 'transactions';

  // Boxes
  Box<Account>? _accountsBox;
  Box<Transaction>? _transactionsBox;

  /// Initialize Hive database
  Future<void> initialize() async {
    try {
      // Initialize Hive
      await Hive.initFlutter();

      // Register adapters
      if (!Hive.isAdapterRegistered(0)) {
        Hive.registerAdapter(AccountTypeAdapter());
      }
      if (!Hive.isAdapterRegistered(1)) {
        Hive.registerAdapter(AccountAdapter());
      }
      if (!Hive.isAdapterRegistered(2)) {
        Hive.registerAdapter(TransactionTypeAdapter());
      }
      if (!Hive.isAdapterRegistered(3)) {
        Hive.registerAdapter(TransactionAdapter());
      }

      // Open boxes
      _accountsBox = await Hive.openBox<Account>(_accountsBoxName);
      _transactionsBox = await Hive.openBox<Transaction>(_transactionsBoxName);

      debugPrint('Hive database initialized successfully');
    } catch (e) {
      debugPrint('Error initializing Hive database: $e');
      rethrow;
    }
  }

  /// Get accounts box
  Box<Account> get accountsBox {
    if (_accountsBox == null || !_accountsBox!.isOpen) {
      throw Exception('Accounts box is not initialized');
    }
    return _accountsBox!;
  }

  /// Get transactions box
  Box<Transaction> get transactionsBox {
    if (_transactionsBox == null || !_transactionsBox!.isOpen) {
      throw Exception('Transactions box is not initialized');
    }
    return _transactionsBox!;
  }

  // Account operations
  Future<void> saveAccount(Account account) async {
    try {
      await accountsBox.put(account.id, account);
      debugPrint('Account saved: ${account.id}');
    } catch (e) {
      debugPrint('Error saving account: $e');
      rethrow;
    }
  }

  Future<Account?> getAccount(String id) async {
    try {
      return accountsBox.get(id);
    } catch (e) {
      debugPrint('Error getting account: $e');
      return null;
    }
  }

  Future<List<Account>> getAllAccounts() async {
    try {
      final accounts = accountsBox.values.toList();
      // Sort by creation date (newest first)
      accounts.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      return accounts;
    } catch (e) {
      debugPrint('Error getting all accounts: $e');
      return [];
    }
  }

  Future<void> deleteAccount(String id) async {
    try {
      await accountsBox.delete(id);
      debugPrint('Account deleted: $id');
    } catch (e) {
      debugPrint('Error deleting account: $e');
      rethrow;
    }
  }

  Future<List<Account>> getUnsyncedAccounts() async {
    try {
      return accountsBox.values.where((account) => !account.isSynced).toList();
    } catch (e) {
      debugPrint('Error getting unsynced accounts: $e');
      return [];
    }
  }

  // Transaction operations
  Future<void> saveTransaction(Transaction transaction) async {
    try {
      await transactionsBox.put(transaction.id, transaction);
      debugPrint('Transaction saved: ${transaction.id}');
    } catch (e) {
      debugPrint('Error saving transaction: $e');
      rethrow;
    }
  }

  Future<Transaction?> getTransaction(String id) async {
    try {
      return transactionsBox.get(id);
    } catch (e) {
      debugPrint('Error getting transaction: $e');
      return null;
    }
  }

  Future<List<Transaction>> getAllTransactions({
    String? accountId,
    int? limit,
    int? offset,
  }) async {
    try {
      var transactions = transactionsBox.values.toList();

      // Filter by account if specified
      if (accountId != null) {
        transactions = transactions.where((t) => t.accountId == accountId).toList();
      }

      // Sort by date (newest first)
      transactions.sort((a, b) => b.date.compareTo(a.date));

      // Apply pagination
      if (offset != null) {
        transactions = transactions.skip(offset).toList();
      }
      if (limit != null) {
        transactions = transactions.take(limit).toList();
      }

      return transactions;
    } catch (e) {
      debugPrint('Error getting all transactions: $e');
      return [];
    }
  }

  Future<List<Transaction>> getRecentTransactions(int limit) async {
    try {
      final transactions = transactionsBox.values.toList();
      transactions.sort((a, b) => b.date.compareTo(a.date));
      return transactions.take(limit).toList();
    } catch (e) {
      debugPrint('Error getting recent transactions: $e');
      return [];
    }
  }

  Future<void> deleteTransaction(String id) async {
    try {
      await transactionsBox.delete(id);
      debugPrint('Transaction deleted: $id');
    } catch (e) {
      debugPrint('Error deleting transaction: $e');
      rethrow;
    }
  }

  Future<List<Transaction>> getUnsyncedTransactions() async {
    try {
      return transactionsBox.values.where((transaction) => !transaction.isSynced).toList();
    } catch (e) {
      debugPrint('Error getting unsynced transactions: $e');
      return [];
    }
  }

  Future<List<Transaction>> searchTransactions(String query) async {
    try {
      final transactions = transactionsBox.values.toList();
      final filteredTransactions = transactions.where((transaction) {
        final description = transaction.description.toLowerCase();
        final note = transaction.note?.toLowerCase() ?? '';
        final searchQuery = query.toLowerCase();
        return description.contains(searchQuery) || note.contains(searchQuery);
      }).toList();

      // Sort by date (newest first)
      filteredTransactions.sort((a, b) => b.date.compareTo(a.date));
      return filteredTransactions;
    } catch (e) {
      debugPrint('Error searching transactions: $e');
      return [];
    }
  }

  // Balance calculations
  Future<double> getAccountBalance(String accountId) async {
    try {
      final account = await getAccount(accountId);
      if (account == null) return 0.0;

      final transactions = await getAllTransactions(accountId: accountId);

      double transactionSum = 0.0;
      for (final transaction in transactions) {
        if (transaction.type.isIncome) {
          transactionSum += transaction.amount;
        } else {
          transactionSum -= transaction.amount;
        }
      }

      return account.initialBalance + transactionSum;
    } catch (e) {
      debugPrint('Error calculating account balance: $e');
      return 0.0;
    }
  }

  Future<double> getTotalBalance() async {
    try {
      final accounts = await getAllAccounts();
      double totalBalance = 0.0;

      for (final account in accounts) {
        final accountBalance = await getAccountBalance(account.id);
        totalBalance += accountBalance;
      }

      return totalBalance;
    } catch (e) {
      debugPrint('Error calculating total balance: $e');
      return 0.0;
    }
  }

  // Utility methods
  Future<void> clearAllData() async {
    try {
      await accountsBox.clear();
      await transactionsBox.clear();
      debugPrint('All data cleared');
    } catch (e) {
      debugPrint('Error clearing data: $e');
      rethrow;
    }
  }

  Future<void> close() async {
    try {
      await _accountsBox?.close();
      await _transactionsBox?.close();
      debugPrint('Hive database closed');
    } catch (e) {
      debugPrint('Error closing Hive database: $e');
    }
  }

  // Statistics
  Future<Map<String, dynamic>> getTransactionStatistics({
    String? accountId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final transactions = await getAllTransactions(accountId: accountId);

      // Filter by date range if provided
      final filteredTransactions = transactions.where((transaction) {
        if (startDate != null && transaction.date.isBefore(startDate)) {
          return false;
        }
        if (endDate != null && transaction.date.isAfter(endDate)) {
          return false;
        }
        return true;
      }).toList();

      double totalIncome = 0;
      double totalExpense = 0;
      int incomeCount = 0;
      int expenseCount = 0;

      for (final transaction in filteredTransactions) {
        if (transaction.type.isIncome) {
          totalIncome += transaction.amount;
          incomeCount++;
        } else {
          totalExpense += transaction.amount;
          expenseCount++;
        }
      }

      return {
        'totalIncome': totalIncome,
        'totalExpense': totalExpense,
        'netAmount': totalIncome - totalExpense,
        'incomeCount': incomeCount,
        'expenseCount': expenseCount,
        'totalCount': filteredTransactions.length,
      };
    } catch (e) {
      debugPrint('Error getting transaction statistics: $e');
      return {
        'totalIncome': 0.0,
        'totalExpense': 0.0,
        'netAmount': 0.0,
        'incomeCount': 0,
        'expenseCount': 0,
        'totalCount': 0,
      };
    }
  }
}
