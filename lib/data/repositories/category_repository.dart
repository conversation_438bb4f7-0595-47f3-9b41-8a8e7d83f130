import 'package:hive/hive.dart';
import '../models/category.dart';

class CategoryRepository {
  static const String _boxName = 'categories';
  Box<Category>? _box;

  /// Initialize the repository
  Future<void> initialize() async {
    if (!Hive.isAdapterRegistered(3)) {
      Hive.registerAdapter(CategoryAdapter());
    }
    _box = await Hive.openBox<Category>(_boxName);
    
    // Initialize with default categories if empty
    if (_box!.isEmpty) {
      await _initializeDefaultCategories();
    }
  }

  /// Initialize default categories
  Future<void> _initializeDefaultCategories() async {
    final defaultCategories = DefaultCategories.createDefaultCategories();
    for (final category in defaultCategories) {
      await _box!.put(category.id, category);
    }
  }

  /// Get all categories
  Future<List<Category>> getAllCategories() async {
    await _ensureInitialized();
    return _box!.values
        .where((category) => !category.isDeleted && category.isActive)
        .toList()
      ..sort((a, b) => a.name.compareTo(b.name));
  }

  /// Get category by ID
  Future<Category?> getCategoryById(String id) async {
    await _ensureInitialized();
    final category = _box!.get(id);
    if (category != null && !category.isDeleted) {
      return category;
    }
    return null;
  }

  /// Create a new category
  Future<Category> createCategory(Category category) async {
    await _ensureInitialized();
    
    // Check if name already exists
    final existingCategories = await getAllCategories();
    final nameExists = existingCategories.any(
      (c) => c.name.toLowerCase() == category.name.toLowerCase(),
    );
    
    if (nameExists) {
      throw Exception('Category with this name already exists');
    }
    
    await _box!.put(category.id, category);
    return category;
  }

  /// Update an existing category
  Future<Category> updateCategory(Category category) async {
    await _ensureInitialized();
    
    final existing = await getCategoryById(category.id);
    if (existing == null) {
      throw Exception('Category not found');
    }
    
    // Check if name already exists (excluding current category)
    final existingCategories = await getAllCategories();
    final nameExists = existingCategories.any(
      (c) => c.id != category.id && 
             c.name.toLowerCase() == category.name.toLowerCase(),
    );
    
    if (nameExists) {
      throw Exception('Category with this name already exists');
    }
    
    final updatedCategory = category.copyWith(
      updatedAt: DateTime.now(),
      isSynced: false, // Mark as needing sync
    );
    
    await _box!.put(category.id, updatedCategory);
    return updatedCategory;
  }

  /// Delete a category (soft delete)
  Future<void> deleteCategory(String id) async {
    await _ensureInitialized();
    
    final category = await getCategoryById(id);
    if (category == null) {
      throw Exception('Category not found');
    }
    
    // Don't allow deletion of default categories
    if (category.isDefault) {
      throw Exception('Cannot delete default categories');
    }
    
    final deletedCategory = category.copyWith(
      isDeleted: true,
      updatedAt: DateTime.now(),
      isSynced: false,
    );
    
    await _box!.put(id, deletedCategory);
  }

  /// Get categories for auto-suggestion based on keywords
  Future<List<Category>> getCategoriesForDescription(String description) async {
    await _ensureInitialized();
    
    final categories = await getAllCategories();
    final lowerDescription = description.toLowerCase();
    
    // Score categories based on keyword matches
    final scoredCategories = <MapEntry<Category, int>>[];
    
    for (final category in categories) {
      int score = 0;
      
      // Check exact keyword matches
      for (final keyword in category.keywords) {
        if (lowerDescription.contains(keyword.toLowerCase())) {
          score += 10; // High score for exact keyword match
        }
      }
      
      // Check partial matches
      for (final keyword in category.keywords) {
        if (keyword.toLowerCase().contains(lowerDescription) ||
            lowerDescription.contains(keyword.toLowerCase())) {
          score += 5; // Medium score for partial match
        }
      }
      
      if (score > 0) {
        scoredCategories.add(MapEntry(category, score));
      }
    }
    
    // Sort by score (highest first) and return top 3
    scoredCategories.sort((a, b) => b.value.compareTo(a.value));
    return scoredCategories.take(3).map((e) => e.key).toList();
  }

  /// Get categories with budget limits
  Future<List<Category>> getCategoriesWithBudgets() async {
    await _ensureInitialized();
    final categories = await getAllCategories();
    return categories.where((c) => c.monthlyBudget != null && c.monthlyBudget! > 0).toList();
  }

  /// Search categories by name
  Future<List<Category>> searchCategories(String query) async {
    await _ensureInitialized();
    
    if (query.isEmpty) {
      return getAllCategories();
    }
    
    final categories = await getAllCategories();
    final lowerQuery = query.toLowerCase();
    
    return categories.where((category) {
      return category.name.toLowerCase().contains(lowerQuery) ||
             category.keywords.any((keyword) => 
                 keyword.toLowerCase().contains(lowerQuery));
    }).toList();
  }

  /// Get unsynced categories
  Future<List<Category>> getUnsyncedCategories() async {
    await _ensureInitialized();
    return _box!.values.where((category) => !category.isSynced).toList();
  }

  /// Mark category as synced
  Future<void> markAsSynced(String id, String firebaseId) async {
    await _ensureInitialized();
    
    final category = _box!.get(id);
    if (category != null) {
      final syncedCategory = category.copyWith(
        isSynced: true,
        firebaseId: firebaseId,
      );
      await _box!.put(id, syncedCategory);
    }
  }

  /// Clear all categories (for testing)
  Future<void> clearAll() async {
    await _ensureInitialized();
    await _box!.clear();
  }

  /// Ensure the repository is initialized
  Future<void> _ensureInitialized() async {
    if (_box == null || !_box!.isOpen) {
      await initialize();
    }
  }

  /// Close the repository
  Future<void> close() async {
    await _box?.close();
    _box = null;
  }
}
