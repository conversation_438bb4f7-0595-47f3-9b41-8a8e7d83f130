import 'package:flutter/foundation.dart';
import '../database/hive_database_service.dart';
import '../models/account.dart';
import '../models/account_balance.dart';

class AccountRepository {
  final HiveDatabaseService _databaseService;

  AccountRepository(this._databaseService);

  /// Create a new account
  Future<Account> createAccount({
    required String name,
    required AccountType type,
    required double initialBalance,
  }) async {
    try {
      final account = Account.create(
        name: name,
        type: type,
        initialBalance: initialBalance,
      );

      await _databaseService.saveAccount(account);

      debugPrint('Account created successfully: ${account.id}');
      return account;
    } catch (e) {
      debugPrint('Error creating account: $e');
      throw Exception('Failed to create account. Please try again.');
    }
  }

  /// Get all accounts
  Future<List<Account>> getAccounts() async {
    try {
      return await _databaseService.getAllAccounts();
    } catch (e) {
      debugPrint('Error getting accounts: $e');
      throw Exception('Failed to load accounts. Please try again.');
    }
  }

  /// Get account by ID
  Future<Account?> getAccount(String id) async {
    try {
      return await _databaseService.getAccount(id);
    } catch (e) {
      debugPrint('Error getting account: $e');
      throw Exception('Failed to load account. Please try again.');
    }
  }

  /// Update account
  Future<Account> updateAccount(Account account) async {
    try {
      final updatedAccount = account.markAsNotSynced();
      await _databaseService.saveAccount(updatedAccount);

      debugPrint('Account updated successfully: ${account.id}');
      return updatedAccount;
    } catch (e) {
      debugPrint('Error updating account: $e');
      throw Exception('Failed to update account. Please try again.');
    }
  }

  /// Delete account
  Future<void> deleteAccount(String id) async {
    try {
      await _databaseService.deleteAccount(id);
      debugPrint('Account deleted successfully: $id');
    } catch (e) {
      debugPrint('Error deleting account: $e');
      throw Exception('Failed to delete account. Please try again.');
    }
  }

  /// Get account with calculated balance
  Future<AccountBalance?> getAccountBalance(String accountId) async {
    try {
      final account = await getAccount(accountId);
      if (account == null) return null;

      final currentBalance = await _databaseService.getAccountBalance(accountId);

      // Get transaction count for this account
      final transactions = await _databaseService.getAllTransactions(accountId: accountId);
      final transactionCount = transactions.length;

      // Get last transaction date
      DateTime? lastTransactionDate;
      if (transactions.isNotEmpty) {
        lastTransactionDate = transactions.first.date;
      }

      return AccountBalance(
        account: account,
        currentBalance: currentBalance,
        transactionCount: transactionCount,
        lastTransactionDate: lastTransactionDate,
      );
    } catch (e) {
      debugPrint('Error getting account balance: $e');
      throw Exception('Failed to load account balance. Please try again.');
    }
  }

  /// Get all accounts with their balances
  Future<List<AccountBalance>> getAccountsWithBalances() async {
    try {
      final accounts = await getAccounts();
      final accountBalances = <AccountBalance>[];

      for (final account in accounts) {
        final balance = await getAccountBalance(account.id);
        if (balance != null) {
          accountBalances.add(balance);
        }
      }

      return accountBalances;
    } catch (e) {
      debugPrint('Error getting accounts with balances: $e');
      throw Exception('Failed to load account balances. Please try again.');
    }
  }

  /// Get total balance across all accounts
  Future<double> getTotalBalance() async {
    try {
      return await _databaseService.getTotalBalance();
    } catch (e) {
      debugPrint('Error getting total balance: $e');
      throw Exception('Failed to calculate total balance. Please try again.');
    }
  }

  /// Get accounts that need to be synced
  Future<List<Account>> getUnsyncedAccounts() async {
    try {
      return await _databaseService.getUnsyncedAccounts();
    } catch (e) {
      debugPrint('Error getting unsynced accounts: $e');
      return [];
    }
  }

  /// Mark account as synced
  Future<Account> markAccountAsSynced(Account account, String firebaseId) async {
    try {
      final syncedAccount = account.markAsSynced(firebaseId: firebaseId);
      await _databaseService.saveAccount(syncedAccount);

      debugPrint('Account marked as synced: ${account.id}');
      return syncedAccount;
    } catch (e) {
      debugPrint('Error marking account as synced: $e');
      throw Exception('Failed to update sync status. Please try again.');
    }
  }

  /// Adjust account balance (creates an adjustment transaction)
  Future<void> adjustAccountBalance({
    required String accountId,
    required double adjustmentAmount,
    required String reason,
  }) async {
    try {
      // Get current account
      final account = await getAccount(accountId);
      if (account == null) {
        throw Exception('Account not found');
      }

      // Create adjustment by updating initial balance
      final adjustedAccount = account.copyWith(
        initialBalance: account.initialBalance + adjustmentAmount,
      );

      await updateAccount(adjustedAccount);

      debugPrint('Account balance adjusted: $accountId by $adjustmentAmount');
    } catch (e) {
      debugPrint('Error adjusting account balance: $e');
      throw Exception('Failed to adjust account balance. Please try again.');
    }
  }

  /// Check if account name is unique
  Future<bool> isAccountNameUnique(String name, {String? excludeId}) async {
    try {
      final accounts = await getAccounts();
      return !accounts.any((account) =>
        account.name.toLowerCase() == name.toLowerCase() &&
        account.id != excludeId
      );
    } catch (e) {
      debugPrint('Error checking account name uniqueness: $e');
      return false;
    }
  }

  /// Validate account data
  String? validateAccountData({
    required String name,
    required double initialBalance,
    String? excludeId,
  }) {
    // Check name length
    if (name.trim().isEmpty) {
      return 'Account name is required';
    }

    if (name.trim().length > 50) {
      return 'Account name must be 50 characters or less';
    }

    // Check balance range
    if (initialBalance < -999999.99 || initialBalance > 999999.99) {
      return 'Initial balance must be between -\$999,999.99 and \$999,999.99';
    }

    return null;
  }
}
