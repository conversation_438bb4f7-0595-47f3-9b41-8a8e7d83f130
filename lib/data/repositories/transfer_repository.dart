import 'package:hive/hive.dart';
import '../models/transfer.dart';

class TransferRepository {
  static const String _boxName = 'transfers';
  Box<Transfer>? _box;

  /// Initialize the repository
  Future<void> initialize() async {
    if (!Hive.isAdapterRegistered(4)) {
      Hive.registerAdapter(TransferAdapter());
    }
    _box = await Hive.openBox<Transfer>(_boxName);
  }

  /// Get all transfers
  Future<List<Transfer>> getAllTransfers() async {
    await _ensureInitialized();
    return _box!.values
        .where((transfer) => !transfer.isDeleted)
        .toList()
      ..sort((a, b) => b.date.compareTo(a.date)); // Most recent first
  }

  /// Get transfer by ID
  Future<Transfer?> getTransferById(String id) async {
    await _ensureInitialized();
    final transfer = _box!.get(id);
    if (transfer != null && !transfer.isDeleted) {
      return transfer;
    }
    return null;
  }

  /// Get transfers for a specific account
  Future<List<Transfer>> getTransfersForAccount(String accountId) async {
    await _ensureInitialized();
    return _box!.values
        .where((transfer) => 
            !transfer.isDeleted && 
            transfer.involvesAccount(accountId))
        .toList()
      ..sort((a, b) => b.date.compareTo(a.date));
  }

  /// Get transfers between two specific accounts
  Future<List<Transfer>> getTransfersBetweenAccounts(
    String fromAccountId, 
    String toAccountId,
  ) async {
    await _ensureInitialized();
    return _box!.values
        .where((transfer) => 
            !transfer.isDeleted && 
            ((transfer.fromAccountId == fromAccountId && transfer.toAccountId == toAccountId) ||
             (transfer.fromAccountId == toAccountId && transfer.toAccountId == fromAccountId)))
        .toList()
      ..sort((a, b) => b.date.compareTo(a.date));
  }

  /// Get transfers within a date range
  Future<List<Transfer>> getTransfersInDateRange(
    DateTime startDate, 
    DateTime endDate,
  ) async {
    await _ensureInitialized();
    return _box!.values
        .where((transfer) => 
            !transfer.isDeleted &&
            transfer.date.isAfter(startDate.subtract(const Duration(days: 1))) &&
            transfer.date.isBefore(endDate.add(const Duration(days: 1))))
        .toList()
      ..sort((a, b) => b.date.compareTo(a.date));
  }

  /// Create a new transfer
  Future<Transfer> createTransfer(Transfer transfer) async {
    await _ensureInitialized();
    
    // Validate the transfer
    if (!transfer.isValid) {
      throw Exception('Invalid transfer data');
    }
    
    await _box!.put(transfer.id, transfer);
    return transfer;
  }

  /// Update an existing transfer
  Future<Transfer> updateTransfer(Transfer transfer) async {
    await _ensureInitialized();
    
    final existing = await getTransferById(transfer.id);
    if (existing == null) {
      throw Exception('Transfer not found');
    }
    
    // Validate the updated transfer
    if (!transfer.isValid) {
      throw Exception('Invalid transfer data');
    }
    
    final updatedTransfer = transfer.copyWith(
      isSynced: false, // Mark as needing sync
    );
    
    await _box!.put(transfer.id, updatedTransfer);
    return updatedTransfer;
  }

  /// Delete a transfer (soft delete)
  Future<void> deleteTransfer(String id) async {
    await _ensureInitialized();
    
    final transfer = await getTransferById(id);
    if (transfer == null) {
      throw Exception('Transfer not found');
    }
    
    final deletedTransfer = transfer.copyWith(
      isDeleted: true,
      isSynced: false,
    );
    
    await _box!.put(id, deletedTransfer);
  }

  /// Get recent transfers (last 30 days)
  Future<List<Transfer>> getRecentTransfers({int days = 30}) async {
    await _ensureInitialized();
    final cutoffDate = DateTime.now().subtract(Duration(days: days));
    
    return _box!.values
        .where((transfer) => 
            !transfer.isDeleted && 
            transfer.date.isAfter(cutoffDate))
        .toList()
      ..sort((a, b) => b.date.compareTo(a.date));
  }

  /// Get transfer statistics for an account
  Future<Map<String, dynamic>> getTransferStatsForAccount(String accountId) async {
    await _ensureInitialized();
    final transfers = await getTransfersForAccount(accountId);
    
    double totalIncoming = 0;
    double totalOutgoing = 0;
    int incomingCount = 0;
    int outgoingCount = 0;
    
    for (final transfer in transfers) {
      if (transfer.toAccountId == accountId) {
        totalIncoming += transfer.amount;
        incomingCount++;
      } else if (transfer.fromAccountId == accountId) {
        totalOutgoing += transfer.amount;
        outgoingCount++;
      }
    }
    
    return {
      'totalIncoming': totalIncoming,
      'totalOutgoing': totalOutgoing,
      'incomingCount': incomingCount,
      'outgoingCount': outgoingCount,
      'netTransfer': totalIncoming - totalOutgoing,
      'totalTransfers': transfers.length,
    };
  }

  /// Search transfers by description
  Future<List<Transfer>> searchTransfers(String query) async {
    await _ensureInitialized();
    
    if (query.isEmpty) {
      return getAllTransfers();
    }
    
    final transfers = await getAllTransfers();
    final lowerQuery = query.toLowerCase();
    
    return transfers.where((transfer) {
      final description = transfer.description?.toLowerCase() ?? '';
      return description.contains(lowerQuery);
    }).toList();
  }

  /// Get unsynced transfers
  Future<List<Transfer>> getUnsyncedTransfers() async {
    await _ensureInitialized();
    return _box!.values.where((transfer) => !transfer.isSynced).toList();
  }

  /// Mark transfer as synced
  Future<void> markAsSynced(String id, String firebaseId) async {
    await _ensureInitialized();
    
    final transfer = _box!.get(id);
    if (transfer != null) {
      final syncedTransfer = transfer.copyWith(
        isSynced: true,
        firebaseId: firebaseId,
      );
      await _box!.put(id, syncedTransfer);
    }
  }

  /// Get total transfer amount for a specific month
  Future<double> getMonthlyTransferAmount(DateTime month, String accountId) async {
    await _ensureInitialized();
    
    final startOfMonth = DateTime(month.year, month.month, 1);
    final endOfMonth = DateTime(month.year, month.month + 1, 0);
    
    final transfers = await getTransfersInDateRange(startOfMonth, endOfMonth);
    
    double total = 0;
    for (final transfer in transfers) {
      if (transfer.involvesAccount(accountId)) {
        total += transfer.getImpactOnAccount(accountId).abs();
      }
    }
    
    return total;
  }

  /// Clear all transfers (for testing)
  Future<void> clearAll() async {
    await _ensureInitialized();
    await _box!.clear();
  }

  /// Ensure the repository is initialized
  Future<void> _ensureInitialized() async {
    if (_box == null || !_box!.isOpen) {
      await initialize();
    }
  }

  /// Close the repository
  Future<void> close() async {
    await _box?.close();
    _box = null;
  }
}
