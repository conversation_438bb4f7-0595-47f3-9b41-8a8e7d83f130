import 'package:flutter/foundation.dart';
import '../database/hive_database_service.dart';
import '../models/transaction.dart';

class TransactionRepository {
  final HiveDatabaseService _databaseService;

  TransactionRepository(this._databaseService);

  /// Create a new transaction
  Future<Transaction> createTransaction({
    required String accountId,
    required TransactionType type,
    required double amount,
    required String description,
    DateTime? date,
    String? note,
  }) async {
    try {
      final transaction = Transaction.create(
        accountId: accountId,
        type: type,
        amount: amount,
        description: description,
        date: date,
        note: note,
      );

      await _databaseService.saveTransaction(transaction);

      debugPrint('Transaction created successfully: ${transaction.id}');
      return transaction;
    } catch (e) {
      debugPrint('Error creating transaction: $e');
      throw Exception('Failed to create transaction. Please try again.');
    }
  }

  /// Create an income transaction
  Future<Transaction> createIncome({
    required String accountId,
    required double amount,
    required String description,
    DateTime? date,
    String? note,
  }) async {
    return createTransaction(
      accountId: accountId,
      type: TransactionType.income,
      amount: amount,
      description: description,
      date: date,
      note: note,
    );
  }

  /// Create an expense transaction
  Future<Transaction> createExpense({
    required String accountId,
    required double amount,
    required String description,
    DateTime? date,
    String? note,
  }) async {
    return createTransaction(
      accountId: accountId,
      type: TransactionType.expense,
      amount: amount,
      description: description,
      date: date,
      note: note,
    );
  }

  /// Get all transactions
  Future<List<Transaction>> getTransactions({
    String? accountId,
    int? limit,
    int? offset,
  }) async {
    try {
      return await _databaseService.getAllTransactions(
        accountId: accountId,
        limit: limit,
        offset: offset,
      );
    } catch (e) {
      debugPrint('Error getting transactions: $e');
      throw Exception('Failed to load transactions. Please try again.');
    }
  }

  /// Get recent transactions
  Future<List<Transaction>> getRecentTransactions(int limit) async {
    try {
      return await _databaseService.getRecentTransactions(limit);
    } catch (e) {
      debugPrint('Error getting recent transactions: $e');
      throw Exception('Failed to load recent transactions. Please try again.');
    }
  }

  /// Get transaction by ID
  Future<Transaction?> getTransaction(String id) async {
    try {
      return await _databaseService.getTransaction(id);
    } catch (e) {
      debugPrint('Error getting transaction: $e');
      throw Exception('Failed to load transaction. Please try again.');
    }
  }

  /// Update transaction
  Future<Transaction> updateTransaction(Transaction transaction) async {
    try {
      final updatedTransaction = transaction.markAsNotSynced();
      await _databaseService.saveTransaction(updatedTransaction);

      debugPrint('Transaction updated successfully: ${transaction.id}');
      return updatedTransaction;
    } catch (e) {
      debugPrint('Error updating transaction: $e');
      throw Exception('Failed to update transaction. Please try again.');
    }
  }

  /// Delete transaction
  Future<void> deleteTransaction(String id) async {
    try {
      await _databaseService.deleteTransaction(id);
      debugPrint('Transaction deleted successfully: $id');
    } catch (e) {
      debugPrint('Error deleting transaction: $e');
      throw Exception('Failed to delete transaction. Please try again.');
    }
  }

  /// Search transactions
  Future<List<Transaction>> searchTransactions(String query) async {
    try {
      if (query.trim().isEmpty) {
        return await getTransactions();
      }

      return await _databaseService.searchTransactions(query.trim());
    } catch (e) {
      debugPrint('Error searching transactions: $e');
      throw Exception('Failed to search transactions. Please try again.');
    }
  }

  /// Get transactions that need to be synced
  Future<List<Transaction>> getUnsyncedTransactions() async {
    try {
      return await _databaseService.getUnsyncedTransactions();
    } catch (e) {
      debugPrint('Error getting unsynced transactions: $e');
      return [];
    }
  }

  /// Mark transaction as synced
  Future<Transaction> markTransactionAsSynced(Transaction transaction, String firebaseId) async {
    try {
      final syncedTransaction = transaction.markAsSynced(firebaseId: firebaseId);
      await _databaseService.saveTransaction(syncedTransaction);

      debugPrint('Transaction marked as synced: ${transaction.id}');
      return syncedTransaction;
    } catch (e) {
      debugPrint('Error marking transaction as synced: $e');
      throw Exception('Failed to update sync status. Please try again.');
    }
  }

  /// Get transactions grouped by date
  Future<Map<String, List<Transaction>>> getTransactionsGroupedByDate({
    String? accountId,
    int? limit,
  }) async {
    try {
      final transactions = await getTransactions(
        accountId: accountId,
        limit: limit,
      );

      final Map<String, List<Transaction>> groupedTransactions = {};

      for (final transaction in transactions) {
        final dateKey = _getDateKey(transaction.date);

        if (groupedTransactions.containsKey(dateKey)) {
          groupedTransactions[dateKey]!.add(transaction);
        } else {
          groupedTransactions[dateKey] = [transaction];
        }
      }

      return groupedTransactions;
    } catch (e) {
      debugPrint('Error getting grouped transactions: $e');
      throw Exception('Failed to load transactions. Please try again.');
    }
  }

  /// Get recent transaction descriptions for suggestions
  Future<List<String>> getRecentDescriptions({int limit = 10}) async {
    try {
      final transactions = await getRecentTransactions(50); // Get more to filter unique
      final descriptions = transactions
          .map((t) => t.description)
          .toSet() // Remove duplicates
          .take(limit)
          .toList();

      return descriptions;
    } catch (e) {
      debugPrint('Error getting recent descriptions: $e');
      return [];
    }
  }

  /// Get transaction statistics
  Future<Map<String, dynamic>> getTransactionStatistics({
    String? accountId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      return await _databaseService.getTransactionStatistics(
        accountId: accountId,
        startDate: startDate,
        endDate: endDate,
      );
    } catch (e) {
      debugPrint('Error getting transaction statistics: $e');
      return {
        'totalIncome': 0.0,
        'totalExpense': 0.0,
        'netAmount': 0.0,
        'incomeCount': 0,
        'expenseCount': 0,
        'totalCount': 0,
      };
    }
  }

  /// Validate transaction data
  String? validateTransactionData({
    required double amount,
    required String description,
  }) {
    // Check amount
    if (amount <= 0) {
      return 'Amount must be greater than zero';
    }

    if (amount > 999999.99) {
      return 'Amount must be less than \$999,999.99';
    }

    // Check description
    if (description.trim().isEmpty) {
      return 'Description is required';
    }

    if (description.trim().length > 100) {
      return 'Description must be 100 characters or less';
    }

    return null;
  }

  /// Helper method to get date key for grouping
  String _getDateKey(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final transactionDate = DateTime(date.year, date.month, date.day);

    if (transactionDate == today) {
      return 'Today';
    } else if (transactionDate == yesterday) {
      return 'Yesterday';
    } else if (now.difference(date).inDays < 7) {
      // This week
      const weekdays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
      return weekdays[date.weekday - 1];
    } else {
      // Older dates
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}
