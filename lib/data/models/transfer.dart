import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hive/hive.dart';

part 'transfer.freezed.dart';
part 'transfer.g.dart';

@freezed
@HiveType(typeId: 4) // Using typeId 4 for transfers
class Transfer with _$Transfer {
  const factory Transfer({
    @HiveField(0) required String id,
    @HiveField(1) required double amount,
    @HiveField(2) required String fromAccountId,
    @HiveField(3) required String toAccountId,
    @HiveField(4) String? description,
    @HiveField(5) required DateTime date,
    @HiveField(6) required DateTime createdAt,
    @HiveField(7) @Default(false) bool isSynced,
    @HiveField(8) @Default(false) bool isDeleted,
    @HiveField(9) String? firebaseId,
  }) = _Transfer;

  factory Transfer.fromJson(Map<String, dynamic> json) => _$TransferFromJson(json);
}

/// Transfer validation and business logic
extension TransferValidation on Transfer {
  /// Validate if the transfer is valid
  bool get isValid {
    return amount > 0 && 
           fromAccountId.isNotEmpty && 
           toAccountId.isNotEmpty && 
           fromAccountId != toAccountId;
  }

  /// Get a user-friendly description for the transfer
  String getDisplayDescription() {
    if (description != null && description!.isNotEmpty) {
      return description!;
    }
    return 'Transfer between accounts';
  }

  /// Check if this transfer involves a specific account
  bool involvesAccount(String accountId) {
    return fromAccountId == accountId || toAccountId == accountId;
  }

  /// Get the impact on a specific account (positive for incoming, negative for outgoing)
  double getImpactOnAccount(String accountId) {
    if (toAccountId == accountId) {
      return amount; // Money coming in
    } else if (fromAccountId == accountId) {
      return -amount; // Money going out
    }
    return 0; // Not involved in this transfer
  }
}

/// Transfer creation helper
class TransferHelper {
  /// Create a new transfer with validation
  static Transfer createTransfer({
    required String fromAccountId,
    required String toAccountId,
    required double amount,
    String? description,
    DateTime? date,
  }) {
    if (fromAccountId == toAccountId) {
      throw ArgumentError('Source and destination accounts must be different');
    }
    
    if (amount <= 0) {
      throw ArgumentError('Transfer amount must be greater than zero');
    }

    final now = DateTime.now();
    
    return Transfer(
      id: _generateTransferId(),
      amount: amount,
      fromAccountId: fromAccountId,
      toAccountId: toAccountId,
      description: description,
      date: date ?? now,
      createdAt: now,
    );
  }

  /// Generate a unique transfer ID
  static String _generateTransferId() {
    return 'transfer_${DateTime.now().millisecondsSinceEpoch}';
  }

  /// Validate transfer against account balances
  static bool validateTransferBalance(Transfer transfer, double sourceAccountBalance) {
    return sourceAccountBalance >= transfer.amount;
  }

  /// Get transfer validation error message
  static String? getValidationError(Transfer transfer, double sourceAccountBalance) {
    if (!transfer.isValid) {
      if (transfer.amount <= 0) {
        return 'Transfer amount must be greater than zero';
      }
      if (transfer.fromAccountId == transfer.toAccountId) {
        return 'Source and destination accounts must be different';
      }
      if (transfer.fromAccountId.isEmpty || transfer.toAccountId.isEmpty) {
        return 'Please select both source and destination accounts';
      }
    }
    
    if (!validateTransferBalance(transfer, sourceAccountBalance)) {
      return 'Insufficient funds in source account';
    }
    
    return null; // No validation errors
  }
}

/// Transfer types for categorization
enum TransferType {
  internal,     // Between user's own accounts
  withdrawal,   // ATM withdrawal (bank to cash)
  deposit,      // Cash deposit (cash to bank)
  bankTransfer, // Between different banks
}

extension TransferTypeExtension on TransferType {
  String get displayName {
    switch (this) {
      case TransferType.internal:
        return 'Internal Transfer';
      case TransferType.withdrawal:
        return 'ATM Withdrawal';
      case TransferType.deposit:
        return 'Cash Deposit';
      case TransferType.bankTransfer:
        return 'Bank Transfer';
    }
  }

  String get description {
    switch (this) {
      case TransferType.internal:
        return 'Move money between your accounts';
      case TransferType.withdrawal:
        return 'Withdraw cash from bank account';
      case TransferType.deposit:
        return 'Deposit cash into bank account';
      case TransferType.bankTransfer:
        return 'Transfer between different banks';
    }
  }

  String get icon {
    switch (this) {
      case TransferType.internal:
        return '↔️';
      case TransferType.withdrawal:
        return '🏧';
      case TransferType.deposit:
        return '🏦';
      case TransferType.bankTransfer:
        return '🔄';
    }
  }
}
