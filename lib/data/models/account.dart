import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hive/hive.dart';

part 'account.freezed.dart';
part 'account.g.dart';

@HiveType(typeId: 0)
enum AccountType {
  @HiveField(0)
  @JsonValue('cash')
  cash,
  @HiveField(1)
  @JsonValue('bank')
  bank,
}

extension AccountTypeExtension on AccountType {
  String get displayName {
    switch (this) {
      case AccountType.cash:
        return 'Cash';
      case AccountType.bank:
        return 'Bank';
    }
  }

  String get icon {
    switch (this) {
      case AccountType.cash:
        return '💵';
      case AccountType.bank:
        return '🏦';
    }
  }
}

@freezed
@HiveType(typeId: 1)
class Account with _$Account {
  const factory Account({
    @HiveField(0) required String id,
    @HiveField(1) required String name,
    @HiveField(2) required AccountType type,
    @HiveField(3) required double initialBalance,
    @HiveField(4) required DateTime createdAt,
    @HiveField(5) required DateTime updatedAt,
    @HiveField(6) @Default(false) bool isSynced,
    @HiveField(7) String? firebaseId,
    @HiveField(8) DateTime? lastSyncedAt,
  }) = _Account;

  factory Account.fromJson(Map<String, dynamic> json) => _$AccountFromJson(json);

  /// Create a new account with generated ID and timestamps
  factory Account.create({
    required String name,
    required AccountType type,
    required double initialBalance,
  }) {
    final now = DateTime.now();
    return Account(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: name,
      type: type,
      initialBalance: initialBalance,
      createdAt: now,
      updatedAt: now,
    );
  }
}

/// Extension methods for Account
extension AccountExtension on Account {
  /// Create a copy with updated sync status
  Account markAsSynced({
    required String firebaseId,
  }) {
    return copyWith(
      isSynced: true,
      firebaseId: firebaseId,
      lastSyncedAt: DateTime.now(),
    );
  }

  /// Create a copy marked as not synced (for local changes)
  Account markAsNotSynced() {
    return copyWith(
      isSynced: false,
      updatedAt: DateTime.now(),
    );
  }

  /// Convert to map for SQLite storage
  Map<String, dynamic> toSqliteMap() {
    return {
      'id': id,
      'name': name,
      'type': type.name,
      'initial_balance': initialBalance,
      'created_at': createdAt.millisecondsSinceEpoch,
      'updated_at': updatedAt.millisecondsSinceEpoch,
      'is_synced': isSynced ? 1 : 0,
      'firebase_id': firebaseId,
      'last_synced_at': lastSyncedAt?.millisecondsSinceEpoch,
    };
  }

  /// Create from SQLite map
  static Account fromSqliteMap(Map<String, dynamic> map) {
    return Account(
      id: map['id'] as String,
      name: map['name'] as String,
      type: AccountType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => AccountType.bank,
      ),
      initialBalance: (map['initial_balance'] as num).toDouble(),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at'] as int),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updated_at'] as int),
      isSynced: (map['is_synced'] as int) == 1,
      firebaseId: map['firebase_id'] as String?,
      lastSyncedAt: map['last_synced_at'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['last_synced_at'] as int)
          : null,
    );
  }

  /// Convert to Firebase document
  Map<String, dynamic> toFirebaseMap() {
    return {
      'id': id,
      'name': name,
      'type': type.name,
      'initialBalance': initialBalance,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// Create from Firebase document
  static Account fromFirebaseMap(Map<String, dynamic> map, String firebaseId) {
    return Account(
      id: map['id'] as String,
      name: map['name'] as String,
      type: AccountType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => AccountType.bank,
      ),
      initialBalance: (map['initialBalance'] as num).toDouble(),
      createdAt: DateTime.parse(map['createdAt'] as String),
      updatedAt: DateTime.parse(map['updatedAt'] as String),
      isSynced: true,
      firebaseId: firebaseId,
      lastSyncedAt: DateTime.now(),
    );
  }
}
