// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'transaction.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

Transaction _$TransactionFromJson(Map<String, dynamic> json) {
  return _Transaction.fromJson(json);
}

/// @nodoc
mixin _$Transaction {
  @HiveField(0)
  String get id => throw _privateConstructorUsedError;
  @HiveField(1)
  String get accountId => throw _privateConstructorUsedError;
  @HiveField(2)
  TransactionType get type => throw _privateConstructorUsedError;
  @HiveField(3)
  double get amount => throw _privateConstructorUsedError;
  @HiveField(4)
  String get description => throw _privateConstructorUsedError;
  @HiveField(5)
  DateTime get date => throw _privateConstructorUsedError;
  @HiveField(6)
  DateTime get createdAt => throw _privateConstructorUsedError;
  @HiveField(7)
  DateTime get updatedAt => throw _privateConstructorUsedError;
  @HiveField(8)
  String? get note => throw _privateConstructorUsedError;
  @HiveField(9)
  bool get isSynced => throw _privateConstructorUsedError;
  @HiveField(10)
  String? get firebaseId => throw _privateConstructorUsedError;
  @HiveField(11)
  DateTime? get lastSyncedAt => throw _privateConstructorUsedError;
  @HiveField(12)
  String? get categoryId => throw _privateConstructorUsedError;

  /// Serializes this Transaction to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Transaction
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TransactionCopyWith<Transaction> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TransactionCopyWith<$Res> {
  factory $TransactionCopyWith(
          Transaction value, $Res Function(Transaction) then) =
      _$TransactionCopyWithImpl<$Res, Transaction>;
  @useResult
  $Res call(
      {@HiveField(0) String id,
      @HiveField(1) String accountId,
      @HiveField(2) TransactionType type,
      @HiveField(3) double amount,
      @HiveField(4) String description,
      @HiveField(5) DateTime date,
      @HiveField(6) DateTime createdAt,
      @HiveField(7) DateTime updatedAt,
      @HiveField(8) String? note,
      @HiveField(9) bool isSynced,
      @HiveField(10) String? firebaseId,
      @HiveField(11) DateTime? lastSyncedAt,
      @HiveField(12) String? categoryId});
}

/// @nodoc
class _$TransactionCopyWithImpl<$Res, $Val extends Transaction>
    implements $TransactionCopyWith<$Res> {
  _$TransactionCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Transaction
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? accountId = null,
    Object? type = null,
    Object? amount = null,
    Object? description = null,
    Object? date = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? note = freezed,
    Object? isSynced = null,
    Object? firebaseId = freezed,
    Object? lastSyncedAt = freezed,
    Object? categoryId = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      accountId: null == accountId
          ? _value.accountId
          : accountId // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as TransactionType,
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      date: null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      note: freezed == note
          ? _value.note
          : note // ignore: cast_nullable_to_non_nullable
              as String?,
      isSynced: null == isSynced
          ? _value.isSynced
          : isSynced // ignore: cast_nullable_to_non_nullable
              as bool,
      firebaseId: freezed == firebaseId
          ? _value.firebaseId
          : firebaseId // ignore: cast_nullable_to_non_nullable
              as String?,
      lastSyncedAt: freezed == lastSyncedAt
          ? _value.lastSyncedAt
          : lastSyncedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      categoryId: freezed == categoryId
          ? _value.categoryId
          : categoryId // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TransactionImplCopyWith<$Res>
    implements $TransactionCopyWith<$Res> {
  factory _$$TransactionImplCopyWith(
          _$TransactionImpl value, $Res Function(_$TransactionImpl) then) =
      __$$TransactionImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@HiveField(0) String id,
      @HiveField(1) String accountId,
      @HiveField(2) TransactionType type,
      @HiveField(3) double amount,
      @HiveField(4) String description,
      @HiveField(5) DateTime date,
      @HiveField(6) DateTime createdAt,
      @HiveField(7) DateTime updatedAt,
      @HiveField(8) String? note,
      @HiveField(9) bool isSynced,
      @HiveField(10) String? firebaseId,
      @HiveField(11) DateTime? lastSyncedAt,
      @HiveField(12) String? categoryId});
}

/// @nodoc
class __$$TransactionImplCopyWithImpl<$Res>
    extends _$TransactionCopyWithImpl<$Res, _$TransactionImpl>
    implements _$$TransactionImplCopyWith<$Res> {
  __$$TransactionImplCopyWithImpl(
      _$TransactionImpl _value, $Res Function(_$TransactionImpl) _then)
      : super(_value, _then);

  /// Create a copy of Transaction
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? accountId = null,
    Object? type = null,
    Object? amount = null,
    Object? description = null,
    Object? date = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? note = freezed,
    Object? isSynced = null,
    Object? firebaseId = freezed,
    Object? lastSyncedAt = freezed,
    Object? categoryId = freezed,
  }) {
    return _then(_$TransactionImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as String,
      accountId: null == accountId
          ? _value.accountId
          : accountId // ignore: cast_nullable_to_non_nullable
              as String,
      type: null == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as TransactionType,
      amount: null == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      date: null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      note: freezed == note
          ? _value.note
          : note // ignore: cast_nullable_to_non_nullable
              as String?,
      isSynced: null == isSynced
          ? _value.isSynced
          : isSynced // ignore: cast_nullable_to_non_nullable
              as bool,
      firebaseId: freezed == firebaseId
          ? _value.firebaseId
          : firebaseId // ignore: cast_nullable_to_non_nullable
              as String?,
      lastSyncedAt: freezed == lastSyncedAt
          ? _value.lastSyncedAt
          : lastSyncedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      categoryId: freezed == categoryId
          ? _value.categoryId
          : categoryId // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TransactionImpl implements _Transaction {
  const _$TransactionImpl(
      {@HiveField(0) required this.id,
      @HiveField(1) required this.accountId,
      @HiveField(2) required this.type,
      @HiveField(3) required this.amount,
      @HiveField(4) required this.description,
      @HiveField(5) required this.date,
      @HiveField(6) required this.createdAt,
      @HiveField(7) required this.updatedAt,
      @HiveField(8) this.note,
      @HiveField(9) this.isSynced = false,
      @HiveField(10) this.firebaseId,
      @HiveField(11) this.lastSyncedAt,
      @HiveField(12) this.categoryId});

  factory _$TransactionImpl.fromJson(Map<String, dynamic> json) =>
      _$$TransactionImplFromJson(json);

  @override
  @HiveField(0)
  final String id;
  @override
  @HiveField(1)
  final String accountId;
  @override
  @HiveField(2)
  final TransactionType type;
  @override
  @HiveField(3)
  final double amount;
  @override
  @HiveField(4)
  final String description;
  @override
  @HiveField(5)
  final DateTime date;
  @override
  @HiveField(6)
  final DateTime createdAt;
  @override
  @HiveField(7)
  final DateTime updatedAt;
  @override
  @HiveField(8)
  final String? note;
  @override
  @JsonKey()
  @HiveField(9)
  final bool isSynced;
  @override
  @HiveField(10)
  final String? firebaseId;
  @override
  @HiveField(11)
  final DateTime? lastSyncedAt;
  @override
  @HiveField(12)
  final String? categoryId;

  @override
  String toString() {
    return 'Transaction(id: $id, accountId: $accountId, type: $type, amount: $amount, description: $description, date: $date, createdAt: $createdAt, updatedAt: $updatedAt, note: $note, isSynced: $isSynced, firebaseId: $firebaseId, lastSyncedAt: $lastSyncedAt, categoryId: $categoryId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TransactionImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.accountId, accountId) ||
                other.accountId == accountId) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.date, date) || other.date == date) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.note, note) || other.note == note) &&
            (identical(other.isSynced, isSynced) ||
                other.isSynced == isSynced) &&
            (identical(other.firebaseId, firebaseId) ||
                other.firebaseId == firebaseId) &&
            (identical(other.lastSyncedAt, lastSyncedAt) ||
                other.lastSyncedAt == lastSyncedAt) &&
            (identical(other.categoryId, categoryId) ||
                other.categoryId == categoryId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      accountId,
      type,
      amount,
      description,
      date,
      createdAt,
      updatedAt,
      note,
      isSynced,
      firebaseId,
      lastSyncedAt,
      categoryId);

  /// Create a copy of Transaction
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TransactionImplCopyWith<_$TransactionImpl> get copyWith =>
      __$$TransactionImplCopyWithImpl<_$TransactionImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TransactionImplToJson(
      this,
    );
  }
}

abstract class _Transaction implements Transaction {
  const factory _Transaction(
      {@HiveField(0) required final String id,
      @HiveField(1) required final String accountId,
      @HiveField(2) required final TransactionType type,
      @HiveField(3) required final double amount,
      @HiveField(4) required final String description,
      @HiveField(5) required final DateTime date,
      @HiveField(6) required final DateTime createdAt,
      @HiveField(7) required final DateTime updatedAt,
      @HiveField(8) final String? note,
      @HiveField(9) final bool isSynced,
      @HiveField(10) final String? firebaseId,
      @HiveField(11) final DateTime? lastSyncedAt,
      @HiveField(12) final String? categoryId}) = _$TransactionImpl;

  factory _Transaction.fromJson(Map<String, dynamic> json) =
      _$TransactionImpl.fromJson;

  @override
  @HiveField(0)
  String get id;
  @override
  @HiveField(1)
  String get accountId;
  @override
  @HiveField(2)
  TransactionType get type;
  @override
  @HiveField(3)
  double get amount;
  @override
  @HiveField(4)
  String get description;
  @override
  @HiveField(5)
  DateTime get date;
  @override
  @HiveField(6)
  DateTime get createdAt;
  @override
  @HiveField(7)
  DateTime get updatedAt;
  @override
  @HiveField(8)
  String? get note;
  @override
  @HiveField(9)
  bool get isSynced;
  @override
  @HiveField(10)
  String? get firebaseId;
  @override
  @HiveField(11)
  DateTime? get lastSyncedAt;
  @override
  @HiveField(12)
  String? get categoryId;

  /// Create a copy of Transaction
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TransactionImplCopyWith<_$TransactionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
