import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hive/hive.dart';

part 'category.freezed.dart';
part 'category.g.dart';

@freezed
@HiveType(typeId: 3) // Using typeId 3 for categories
class Category with _$Category {
  const factory Category({
    @HiveField(0) required String id,
    @HiveField(1) required String name,
    @HiveField(2) required String icon,
    @HiveField(3) required String color,
    @HiveField(4) @Default(false) bool isDefault,
    @HiveField(5) required DateTime createdAt,
    @HiveField(6) required DateTime updatedAt,
    @HiveField(7) @Default(false) bool isSynced,
    @HiveField(8) @Default(false) bool isDeleted,
    @HiveField(9) String? firebaseId,
    @HiveField(10) @Default([]) List<String> keywords,
    @HiveField(11) double? monthlyBudget,
    @HiveField(12) @Default(true) bool isActive,
  }) = _Category;

  factory Category.fromJson(Map<String, dynamic> json) => _$CategoryFromJson(json);
}

/// Pre-defined essential categories that come with the app
class DefaultCategories {
  static const List<Category> essentialCategories = [
    Category(
      id: 'food_dining',
      name: 'Food & Dining',
      icon: '🍽️',
      color: '#FF9800', // Orange
      isDefault: true,
      keywords: ['restaurant', 'food', 'grocery', 'cafe', 'lunch', 'dinner', 'starbucks', 'mcdonalds', 'pizza'],
      createdAt: null, // Will be set when first created
      updatedAt: null, // Will be set when first created
    ),
    Category(
      id: 'transportation',
      name: 'Transportation',
      icon: '🚗',
      color: '#2196F3', // Blue
      isDefault: true,
      keywords: ['gas', 'fuel', 'uber', 'taxi', 'bus', 'train', 'parking', 'shell', 'exxon', 'bp'],
      createdAt: null,
      updatedAt: null,
    ),
    Category(
      id: 'shopping',
      name: 'Shopping',
      icon: '🛒',
      color: '#9C27B0', // Purple
      isDefault: true,
      keywords: ['amazon', 'store', 'mall', 'clothes', 'electronics', 'walmart', 'target', 'ebay'],
      createdAt: null,
      updatedAt: null,
    ),
    Category(
      id: 'bills_utilities',
      name: 'Bills & Utilities',
      icon: '💡',
      color: '#F44336', // Red
      isDefault: true,
      keywords: ['electricity', 'water', 'internet', 'phone', 'rent', 'utility', 'bill', 'payment'],
      createdAt: null,
      updatedAt: null,
    ),
    Category(
      id: 'health_medical',
      name: 'Health & Medical',
      icon: '🏥',
      color: '#4CAF50', // Green
      isDefault: true,
      keywords: ['doctor', 'medicine', 'pharmacy', 'hospital', 'clinic', 'cvs', 'walgreens'],
      createdAt: null,
      updatedAt: null,
    ),
    Category(
      id: 'entertainment',
      name: 'Entertainment',
      icon: '🎬',
      color: '#E91E63', // Pink
      isDefault: true,
      keywords: ['movie', 'game', 'netflix', 'spotify', 'concert', 'theater', 'cinema'],
      createdAt: null,
      updatedAt: null,
    ),
    Category(
      id: 'education',
      name: 'Education',
      icon: '📚',
      color: '#3F51B5', // Indigo
      isDefault: true,
      keywords: ['book', 'course', 'school', 'university', 'training', 'education', 'tuition'],
      createdAt: null,
      updatedAt: null,
    ),
    Category(
      id: 'personal_care',
      name: 'Personal Care',
      icon: '✂️',
      color: '#009688', // Teal
      isDefault: true,
      keywords: ['salon', 'spa', 'cosmetics', 'hygiene', 'haircut', 'beauty', 'barber'],
      createdAt: null,
      updatedAt: null,
    ),
    Category(
      id: 'other',
      name: 'Other',
      icon: '📋',
      color: '#757575', // Gray
      isDefault: true,
      keywords: [],
      createdAt: null,
      updatedAt: null,
    ),
  ];

  /// Create default categories with current timestamp
  static List<Category> createDefaultCategories() {
    final now = DateTime.now();
    return essentialCategories.map((category) => category.copyWith(
      createdAt: now,
      updatedAt: now,
    )).toList();
  }
}

/// Available icons for categories
class CategoryIcons {
  static const List<String> availableIcons = [
    // Food & Dining
    '🍽️', '🍕', '🍔', '🍟', '🌮', '🍜', '🍱', '🥗', '🍰', '☕',
    
    // Transportation
    '🚗', '🚕', '🚌', '🚇', '✈️', '⛽', '🚲', '🛵', '🚁', '🚢',
    
    // Shopping
    '🛒', '🛍️', '👕', '👔', '👗', '👠', '💻', '📱', '🎮', '📚',
    
    // Bills & Utilities
    '💡', '🏠', '📞', '💻', '📺', '🔌', '💧', '🔥', '📄', '💳',
    
    // Health & Medical
    '🏥', '💊', '🩺', '💉', '🦷', '👓', '🧴', '🏃', '🧘', '💪',
    
    // Entertainment
    '🎬', '🎵', '🎮', '🎪', '🎨', '📖', '🎯', '🎲', '🎸', '🎤',
    
    // Education
    '📚', '✏️', '📝', '🎓', '🏫', '💼', '📊', '🔬', '🧮', '📐',
    
    // Personal Care
    '✂️', '💄', '🧴', '🪒', '🧼', '🦷', '💅', '🧖', '💆', '🛁',
    
    // General
    '📋', '📁', '💰', '🎯', '⭐', '❤️', '🔥', '⚡', '🌟', '🎉',
  ];
}

/// Available colors for categories
class CategoryColors {
  static const List<String> availableColors = [
    '#F44336', // Red
    '#E91E63', // Pink
    '#9C27B0', // Purple
    '#673AB7', // Deep Purple
    '#3F51B5', // Indigo
    '#2196F3', // Blue
    '#03A9F4', // Light Blue
    '#00BCD4', // Cyan
    '#009688', // Teal
    '#4CAF50', // Green
    '#8BC34A', // Light Green
    '#CDDC39', // Lime
    '#FFEB3B', // Yellow
    '#FFC107', // Amber
    '#FF9800', // Orange
    '#FF5722', // Deep Orange
    '#795548', // Brown
    '#607D8B', // Blue Gray
    '#757575', // Gray
    '#9E9E9E', // Light Gray
  ];
}
