import 'package:freezed_annotation/freezed_annotation.dart';
import 'account.dart';

part 'account_balance.freezed.dart';

/// Model representing an account with its calculated balance
@freezed
class AccountBalance with _$AccountBalance {
  const factory AccountBalance({
    required Account account,
    required double currentBalance,
    required int transactionCount,
    DateTime? lastTransactionDate,
  }) = _AccountBalance;
}

/// Extension methods for AccountBalance
extension AccountBalanceExtension on AccountBalance {
  /// Check if balance is positive
  bool get isPositive => currentBalance > 0;
  
  /// Check if balance is negative
  bool get isNegative => currentBalance < 0;
  
  /// Check if balance is zero
  bool get isZero => currentBalance == 0;
  
  /// Get balance color based on value
  String get balanceColorHex {
    if (isPositive) return '#4CAF50'; // Green
    if (isNegative) return '#F44336'; // Red
    return '#9E9E9E'; // Gray
  }
  
  /// Get balance status text
  String get balanceStatus {
    if (isPositive) return 'Positive';
    if (isNegative) return 'Negative';
    return 'Zero';
  }
  
  /// Check if account has transactions
  bool get hasTransactions => transactionCount > 0;
  
  /// Get account type display info
  String get typeDisplay => account.type.displayName;
  
  /// Get account type icon
  String get typeIcon => account.type.icon;
}
