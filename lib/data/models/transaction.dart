import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hive/hive.dart';

part 'transaction.freezed.dart';
part 'transaction.g.dart';

@HiveType(typeId: 2)
enum TransactionType {
  @HiveField(0)
  @JsonValue('income')
  income,
  @HiveField(1)
  @JsonValue('expense')
  expense,
}

extension TransactionTypeExtension on TransactionType {
  String get displayName {
    switch (this) {
      case TransactionType.income:
        return 'Income';
      case TransactionType.expense:
        return 'Expense';
    }
  }

  String get icon {
    switch (this) {
      case TransactionType.income:
        return '+';
      case TransactionType.expense:
        return '-';
    }
  }

  bool get isIncome => this == TransactionType.income;
  bool get isExpense => this == TransactionType.expense;
}

@freezed
@HiveType(typeId: 3)
class Transaction with _$Transaction {
  const factory Transaction({
    @HiveField(0) required String id,
    @HiveField(1) required String accountId,
    @HiveField(2) required TransactionType type,
    @HiveField(3) required double amount,
    @HiveField(4) required String description,
    @HiveField(5) required DateTime date,
    @HiveField(6) required DateTime createdAt,
    @HiveField(7) required DateTime updatedAt,
    @HiveField(8) String? note,
    @HiveField(9) @Default(false) bool isSynced,
    @HiveField(10) String? firebaseId,
    @HiveField(11) DateTime? lastSyncedAt,
    @HiveField(12) String? categoryId, // Phase 2: Category support
  }) = _Transaction;

  factory Transaction.fromJson(Map<String, dynamic> json) => _$TransactionFromJson(json);

  /// Create a new transaction with generated ID and timestamps
  factory Transaction.create({
    required String accountId,
    required TransactionType type,
    required double amount,
    required String description,
    DateTime? date,
    String? note,
    String? categoryId,
  }) {
    final now = DateTime.now();
    return Transaction(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      accountId: accountId,
      type: type,
      amount: amount,
      description: description,
      date: date ?? now,
      createdAt: now,
      updatedAt: now,
      note: note,
      categoryId: categoryId,
    );
  }

  /// Create an income transaction
  factory Transaction.income({
    required String accountId,
    required double amount,
    required String description,
    DateTime? date,
    String? note,
    String? categoryId,
  }) {
    return Transaction.create(
      accountId: accountId,
      type: TransactionType.income,
      amount: amount,
      description: description,
      date: date,
      note: note,
      categoryId: categoryId,
    );
  }

  /// Create an expense transaction
  factory Transaction.expense({
    required String accountId,
    required double amount,
    required String description,
    DateTime? date,
    String? note,
    String? categoryId,
  }) {
    return Transaction.create(
      accountId: accountId,
      type: TransactionType.expense,
      amount: amount,
      description: description,
      date: date,
      note: note,
      categoryId: categoryId,
    );
  }
}

/// Extension methods for Transaction
extension TransactionExtension on Transaction {
  /// Get the signed amount (positive for income, negative for expense)
  double get signedAmount {
    return type.isIncome ? amount : -amount;
  }

  /// Create a copy with updated sync status
  Transaction markAsSynced({
    required String firebaseId,
  }) {
    return copyWith(
      isSynced: true,
      firebaseId: firebaseId,
      lastSyncedAt: DateTime.now(),
    );
  }

  /// Create a copy marked as not synced (for local changes)
  Transaction markAsNotSynced() {
    return copyWith(
      isSynced: false,
      updatedAt: DateTime.now(),
    );
  }

  /// Convert to map for SQLite storage
  Map<String, dynamic> toSqliteMap() {
    return {
      'id': id,
      'account_id': accountId,
      'type': type.name,
      'amount': amount,
      'description': description,
      'note': note,
      'date': date.millisecondsSinceEpoch,
      'created_at': createdAt.millisecondsSinceEpoch,
      'updated_at': updatedAt.millisecondsSinceEpoch,
      'is_synced': isSynced ? 1 : 0,
      'firebase_id': firebaseId,
      'last_synced_at': lastSyncedAt?.millisecondsSinceEpoch,
      'category_id': categoryId,
    };
  }

  /// Create from SQLite map
  static Transaction fromSqliteMap(Map<String, dynamic> map) {
    return Transaction(
      id: map['id'] as String,
      accountId: map['account_id'] as String,
      type: TransactionType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => TransactionType.expense,
      ),
      amount: (map['amount'] as num).toDouble(),
      description: map['description'] as String,
      note: map['note'] as String?,
      date: DateTime.fromMillisecondsSinceEpoch(map['date'] as int),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at'] as int),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updated_at'] as int),
      isSynced: (map['is_synced'] as int) == 1,
      firebaseId: map['firebase_id'] as String?,
      lastSyncedAt: map['last_synced_at'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['last_synced_at'] as int)
          : null,
      categoryId: map['category_id'] as String?,
    );
  }

  /// Convert to Firebase document
  Map<String, dynamic> toFirebaseMap() {
    return {
      'id': id,
      'accountId': accountId,
      'type': type.name,
      'amount': amount,
      'description': description,
      'note': note,
      'date': date.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'categoryId': categoryId,
    };
  }

  /// Create from Firebase document
  static Transaction fromFirebaseMap(Map<String, dynamic> map, String firebaseId) {
    return Transaction(
      id: map['id'] as String,
      accountId: map['accountId'] as String,
      type: TransactionType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => TransactionType.expense,
      ),
      amount: (map['amount'] as num).toDouble(),
      description: map['description'] as String,
      note: map['note'] as String?,
      date: DateTime.parse(map['date'] as String),
      createdAt: DateTime.parse(map['createdAt'] as String),
      updatedAt: DateTime.parse(map['updatedAt'] as String),
      isSynced: true,
      firebaseId: firebaseId,
      lastSyncedAt: DateTime.now(),
      categoryId: map['categoryId'] as String?,
    );
  }
}
