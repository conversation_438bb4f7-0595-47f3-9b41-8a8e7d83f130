/// Application-wide constants
class AppConstants {
  // App Information
  static const String appName = 'Expense Tracker';
  static const String appVersion = '1.0.0';

  // Database
  static const String databaseName = 'expense_tracker.db';
  static const int databaseVersion = 1;

  // Sync
  static const Duration syncRetryDelay = Duration(seconds: 5);
  static const int maxSyncRetries = 3;
  static const Duration syncTimeout = Duration(seconds: 30);

  // UI
  static const double defaultPadding = 16.0;
  static const double cardBorderRadius = 12.0;
  static const double buttonBorderRadius = 8.0;
  static const double minTouchTarget = 48.0;

  // Validation
  static const int maxAccountNameLength = 50;
  static const int maxDescriptionLength = 100;
  static const double maxTransactionAmount = 999999.99;
  static const double minTransactionAmount = -999999.99;

  // Pagination
  static const int transactionsPerPage = 50;
  static const int recentTransactionsCount = 5;

  // Date Formats
  static const String dateFormat = 'MMM dd, yyyy';
  static const String timeFormat = 'h:mm a';
  static const String dateTimeFormat = 'MMM dd, yyyy h:mm a';

  // Currency
  static const String currencySymbol = 'PKR ';
  static const String currencyCode = 'PKR';

  // Firebase Collections
  static const String usersCollection = 'users';
  static const String accountsCollection = 'accounts';
  static const String transactionsCollection = 'transactions';

  // Shared Preferences Keys
  static const String keyUserId = 'user_id';
  static const String keyLastSyncTime = 'last_sync_time';
  static const String keyOfflineMode = 'offline_mode';
  static const String keyFirstLaunch = 'first_launch';
}
