import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../constants/app_constants.dart';

/// Extension methods for DateTime
extension DateTimeExtensions on DateTime {
  /// Format date as "Mar 15, 2024"
  String toDateString() {
    return DateFormat(AppConstants.dateFormat).format(this);
  }
  
  /// Format time as "2:30 PM"
  String toTimeString() {
    return DateFormat(AppConstants.timeFormat).format(this);
  }
  
  /// Format date and time as "Mar 15, 2024 2:30 PM"
  String toDateTimeString() {
    return DateFormat(AppConstants.dateTimeFormat).format(this);
  }
  
  /// Get relative date string (Today, Yesterday, etc.)
  String toRelativeDateString() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final thisDate = DateTime(year, month, day);
    
    if (thisDate == today) {
      return 'Today ${toTimeString()}';
    } else if (thisDate == yesterday) {
      return 'Yesterday';
    } else if (now.difference(this).inDays < 7) {
      return DateFormat('EEEE').format(this); // Day name
    } else {
      return toDateString();
    }
  }
  
  /// Check if date is today
  bool get isToday {
    final now = DateTime.now();
    return year == now.year && month == now.month && day == now.day;
  }
  
  /// Check if date is yesterday
  bool get isYesterday {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return year == yesterday.year && month == yesterday.month && day == yesterday.day;
  }
  
  /// Check if date is in current week
  bool get isThisWeek {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final endOfWeek = startOfWeek.add(const Duration(days: 6));
    return isAfter(startOfWeek.subtract(const Duration(days: 1))) && 
           isBefore(endOfWeek.add(const Duration(days: 1)));
  }
}

/// Extension methods for double (amounts)
extension DoubleExtensions on double {
  /// Format amount as currency string
  String toCurrencyString() {
    final formatter = NumberFormat.currency(
      symbol: AppConstants.currencySymbol,
      decimalDigits: 2,
    );
    return formatter.format(this);
  }
  
  /// Format amount with sign (+ or -)
  String toSignedCurrencyString() {
    final formatted = abs().toCurrencyString();
    if (this > 0) {
      return '+$formatted';
    } else if (this < 0) {
      return '-$formatted';
    } else {
      return formatted;
    }
  }
  
  /// Get color based on amount value
  Color getAmountColor() {
    if (this > 0) {
      return const Color(0xFF4CAF50); // Green for positive
    } else if (this < 0) {
      return const Color(0xFFF44336); // Red for negative
    } else {
      return const Color(0xFF9E9E9E); // Gray for zero
    }
  }
  
  /// Check if amount is valid
  bool get isValidAmount {
    return this >= AppConstants.minTransactionAmount && 
           this <= AppConstants.maxTransactionAmount;
  }
}

/// Extension methods for String
extension StringExtensions on String {
  /// Capitalize first letter
  String get capitalize {
    if (isEmpty) return this;
    return '${this[0].toUpperCase()}${substring(1)}';
  }
  
  /// Check if string is a valid amount
  bool get isValidAmount {
    if (isEmpty) return false;
    final amount = double.tryParse(this);
    return amount != null && amount.isValidAmount;
  }
  
  /// Parse string to double amount
  double? toAmount() {
    if (isEmpty) return null;
    return double.tryParse(this);
  }
  
  /// Truncate string to max length
  String truncate(int maxLength) {
    if (length <= maxLength) return this;
    return '${substring(0, maxLength - 3)}...';
  }
  
  /// Check if string is empty or whitespace only
  bool get isEmptyOrWhitespace {
    return trim().isEmpty;
  }
}

/// Extension methods for BuildContext
extension BuildContextExtensions on BuildContext {
  /// Get theme data
  ThemeData get theme => Theme.of(this);
  
  /// Get color scheme
  ColorScheme get colorScheme => theme.colorScheme;
  
  /// Get text theme
  TextTheme get textTheme => theme.textTheme;
  
  /// Get media query
  MediaQueryData get mediaQuery => MediaQuery.of(this);
  
  /// Get screen size
  Size get screenSize => mediaQuery.size;
  
  /// Get screen width
  double get screenWidth => screenSize.width;
  
  /// Get screen height
  double get screenHeight => screenSize.height;
  
  /// Check if device is tablet
  bool get isTablet => screenWidth > 600;
  
  /// Check if device is in landscape mode
  bool get isLandscape => mediaQuery.orientation == Orientation.landscape;
  
  /// Show snack bar
  void showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(this).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError 
          ? theme.colorScheme.error 
          : theme.colorScheme.primary,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
  
  /// Show success message
  void showSuccess(String message) {
    showSnackBar(message, isError: false);
  }
  
  /// Show error message
  void showError(String message) {
    showSnackBar(message, isError: true);
  }
  
  /// Hide keyboard
  void hideKeyboard() {
    FocusScope.of(this).unfocus();
  }
}

/// Extension methods for List
extension ListExtensions<T> on List<T> {
  /// Get item at index or null if out of bounds
  T? getOrNull(int index) {
    if (index < 0 || index >= length) return null;
    return this[index];
  }
  
  /// Add item if condition is true
  List<T> addIf(bool condition, T item) {
    if (condition) add(item);
    return this;
  }
  
  /// Add items if condition is true
  List<T> addAllIf(bool condition, Iterable<T> items) {
    if (condition) addAll(items);
    return this;
  }
}
