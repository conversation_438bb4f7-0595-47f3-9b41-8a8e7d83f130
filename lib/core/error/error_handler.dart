import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// Centralized error handling system
class ErrorHandler {
  /// Log error with debug information and return user-friendly message
  static String handleError(dynamic error, {String? context}) {
    // Always log the actual error for debugging
    debugPrint('ERROR${context != null ? ' in $context' : ''}: $error');

    // Return user-friendly message based on error type
    return _getUserFriendlyMessage(error);
  }

  /// Get user-friendly error message
  static String _getUserFriendlyMessage(dynamic error) {
    if (error is FirebaseException) {
      return _handleFirebaseError(error);
    }

    if (error is FirebaseAuthException) {
      return _handleAuthError(error);
    }

    if (error is Exception) {
      return _handleGenericException(error);
    }

    // For any other error type
    return 'Something went wrong. Please try again.';
  }

  /// Handle Firebase-specific errors
  static String _handleFirebaseError(FirebaseException error) {
    switch (error.code) {
      case 'permission-denied':
        return 'Access denied. Please check your permissions.';
      case 'unavailable':
        return 'Service temporarily unavailable. Please try again.';
      case 'deadline-exceeded':
        return 'Request timed out. Please check your connection.';
      case 'resource-exhausted':
        return 'Service is busy. Please try again in a moment.';
      case 'failed-precondition':
        return 'Operation failed. Please refresh and try again.';
      case 'aborted':
        return 'Operation was cancelled. Please try again.';
      case 'out-of-range':
        return 'Invalid data range. Please check your input.';
      case 'unimplemented':
        return 'Feature not available. Please try again later.';
      case 'internal':
        return 'Internal error occurred. Please try again.';
      case 'data-loss':
        return 'Data error occurred. Please contact support.';
      case 'unauthenticated':
        return 'Please sign in to continue.';
      default:
        return 'Connection error. Please check your internet and try again.';
    }
  }

  /// Handle authentication errors
  static String _handleAuthError(FirebaseAuthException error) {
    switch (error.code) {
      case 'network-request-failed':
        return 'Network error. Please check your connection.';
      case 'too-many-requests':
        return 'Too many attempts. Please try again later.';
      case 'user-disabled':
        return 'Account has been disabled. Please contact support.';
      case 'user-not-found':
        return 'Account not found. Please check your credentials.';
      case 'wrong-password':
        return 'Incorrect password. Please try again.';
      case 'invalid-email':
        return 'Invalid email address. Please check and try again.';
      case 'email-already-in-use':
        return 'Email is already registered. Please sign in instead.';
      case 'weak-password':
        return 'Password is too weak. Please choose a stronger password.';
      case 'operation-not-allowed':
        return 'Sign-in method not enabled. Please contact support.';
      case 'invalid-credential':
        return 'Invalid credentials. Please try again.';
      case 'account-exists-with-different-credential':
        return 'Account exists with different sign-in method.';
      case 'credential-already-in-use':
        return 'Credential is already associated with another account.';
      case 'requires-recent-login':
        return 'Please sign in again to continue.';
      default:
        return 'Authentication error. Please try again.';
    }
  }

  /// Handle generic exceptions
  static String _handleGenericException(Exception error) {
    final errorString = error.toString().toLowerCase();

    if (errorString.contains('network') || errorString.contains('connection')) {
      return 'Network error. Please check your connection.';
    }

    if (errorString.contains('timeout')) {
      return 'Request timed out. Please try again.';
    }

    if (errorString.contains('format') || errorString.contains('parse')) {
      return 'Data format error. Please try again.';
    }

    if (errorString.contains('not found')) {
      return 'Requested data not found.';
    }

    if (errorString.contains('permission') || errorString.contains('access')) {
      return 'Access denied. Please check your permissions.';
    }

    return 'An error occurred. Please try again.';
  }

  /// Handle async operation errors with context
  static Future<T?> handleAsyncError<T>(
    Future<T> operation, {
    String? context,
    T? fallback,
  }) async {
    try {
      return await operation;
    } catch (error) {
      handleError(error, context: context);
      return fallback;
    }
  }

  /// Handle sync operation errors with context
  static T? handleSyncError<T>(
    T Function() operation, {
    String? context,
    T? fallback,
  }) {
    try {
      return operation();
    } catch (error) {
      handleError(error, context: context);
      return fallback;
    }
  }
}

/// Error types for categorization
enum ErrorType {
  network,
  authentication,
  permission,
  validation,
  notFound,
  timeout,
  unknown,
}

/// Error severity levels
enum ErrorSeverity {
  low,    // User can continue with limited functionality
  medium, // User should retry the operation
  high,   // User needs to take action (e.g., sign in)
  critical, // App functionality is severely impacted
}

/// Structured error information
class AppError {
  final String userMessage;
  final String? technicalMessage;
  final ErrorType type;
  final ErrorSeverity severity;
  final String? context;
  final DateTime timestamp;

  AppError({
    required this.userMessage,
    this.technicalMessage,
    required this.type,
    required this.severity,
    this.context,
    DateTime? timestamp,
  }) : timestamp = timestamp ?? DateTime.now();

  /// Create from exception
  factory AppError.fromException(
    dynamic error, {
    String? context,
  }) {
    final userMessage = ErrorHandler._getUserFriendlyMessage(error);
    final technicalMessage = error.toString();

    ErrorType type = ErrorType.unknown;
    ErrorSeverity severity = ErrorSeverity.medium;

    if (error is FirebaseAuthException) {
      type = ErrorType.authentication;
      severity = ErrorSeverity.high;
    } else if (error is FirebaseException) {
      if (error.code == 'permission-denied') {
        type = ErrorType.permission;
        severity = ErrorSeverity.high;
      } else if (error.code == 'unavailable') {
        type = ErrorType.network;
        severity = ErrorSeverity.medium;
      }
    }

    return AppError(
      userMessage: userMessage,
      technicalMessage: technicalMessage,
      type: type,
      severity: severity,
      context: context,
    );
  }

  @override
  String toString() {
    return 'AppError(userMessage: $userMessage, type: $type, severity: $severity, context: $context)';
  }
}
