import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../core/constants/app_constants.dart';
import '../data/models/account.dart';
import '../data/models/transaction.dart' as app_transaction;

/// Service for managing Firebase Firestore operations
class FirebaseService {
  static FirebaseService? _instance;
  static FirebaseService get instance {
    _instance ??= FirebaseService._internal();
    return _instance!;
  }

  FirebaseService._internal();

  FirebaseFirestore? _firestore;
  String? _userId;

  /// Initialize Firebase service
  Future<void> initialize() async {
    try {
      _firestore = FirebaseFirestore.instance;

      // Configure Firestore settings
      _firestore!.settings = const Settings(
        persistenceEnabled: true,
        cacheSizeBytes: Settings.CACHE_SIZE_UNLIMITED,
      );

      debugPrint('Firebase service initialized successfully');
    } catch (e) {
      debugPrint('Error initializing Firebase service: $e');
      rethrow;
    }
  }

  /// Set current user ID for Firestore operations
  void setUserId(String userId) {
    _userId = userId;
    debugPrint('Firebase service user ID set: $userId');
  }

  /// Get Firestore instance
  FirebaseFirestore get firestore {
    if (_firestore == null) {
      throw Exception('Firebase service not initialized');
    }
    return _firestore!;
  }

  /// Get current user ID
  String get userId {
    if (_userId == null) {
      throw Exception('User ID not set in Firebase service');
    }
    return _userId!;
  }

  /// Check if user is set
  bool get hasUser => _userId != null;

  // Account operations

  /// Save account to Firestore
  Future<String> saveAccount(Account account) async {
    try {
      final accountData = account.toFirebaseMap();
      final docRef = await firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .collection(AppConstants.accountsCollection)
          .add(accountData);

      debugPrint('Account saved to Firebase: ${docRef.id}');
      return docRef.id;
    } catch (e) {
      debugPrint('Error saving account to Firebase: $e');
      rethrow;
    }
  }

  /// Update account in Firestore
  Future<void> updateAccount(String firebaseId, Account account) async {
    try {
      final accountData = account.toFirebaseMap();
      await firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .collection(AppConstants.accountsCollection)
          .doc(firebaseId)
          .update(accountData);

      debugPrint('Account updated in Firebase: $firebaseId');
    } catch (e) {
      debugPrint('Error updating account in Firebase: $e');
      rethrow;
    }
  }

  /// Delete account from Firestore
  Future<void> deleteAccount(String firebaseId) async {
    try {
      await firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .collection(AppConstants.accountsCollection)
          .doc(firebaseId)
          .delete();

      debugPrint('Account deleted from Firebase: $firebaseId');
    } catch (e) {
      debugPrint('Error deleting account from Firebase: $e');
      rethrow;
    }
  }

  /// Get all accounts from Firestore
  Future<List<Account>> getAccounts() async {
    try {
      final snapshot = await firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .collection(AppConstants.accountsCollection)
          .orderBy('createdAt', descending: true)
          .get();

      final accounts = snapshot.docs.map((doc) {
        return AccountExtension.fromFirebaseMap(doc.data(), doc.id);
      }).toList();

      debugPrint('Retrieved ${accounts.length} accounts from Firebase');
      return accounts;
    } catch (e) {
      debugPrint('Error getting accounts from Firebase: $e');
      rethrow;
    }
  }

  /// Get accounts updated after a specific timestamp
  Future<List<Account>> getAccountsUpdatedAfter(DateTime timestamp) async {
    try {
      final snapshot = await firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .collection(AppConstants.accountsCollection)
          .where('updatedAt', isGreaterThan: timestamp.toIso8601String())
          .orderBy('updatedAt', descending: true)
          .get();

      final accounts = snapshot.docs.map((doc) {
        return AccountExtension.fromFirebaseMap(doc.data(), doc.id);
      }).toList();

      debugPrint('Retrieved ${accounts.length} updated accounts from Firebase');
      return accounts;
    } catch (e) {
      debugPrint('Error getting updated accounts from Firebase: $e');
      rethrow;
    }
  }

  // Transaction operations

  /// Save transaction to Firestore
  Future<String> saveTransaction(app_transaction.Transaction transaction) async {
    try {
      final transactionData = transaction.toFirebaseMap();
      final docRef = await firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .collection(AppConstants.transactionsCollection)
          .add(transactionData);

      debugPrint('Transaction saved to Firebase: ${docRef.id}');
      return docRef.id;
    } catch (e) {
      debugPrint('Error saving transaction to Firebase: $e');
      rethrow;
    }
  }

  /// Update transaction in Firestore
  Future<void> updateTransaction(String firebaseId, app_transaction.Transaction transaction) async {
    try {
      final transactionData = transaction.toFirebaseMap();
      await firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .collection(AppConstants.transactionsCollection)
          .doc(firebaseId)
          .update(transactionData);

      debugPrint('Transaction updated in Firebase: $firebaseId');
    } catch (e) {
      debugPrint('Error updating transaction in Firebase: $e');
      rethrow;
    }
  }

  /// Delete transaction from Firestore
  Future<void> deleteTransaction(String firebaseId) async {
    try {
      await firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .collection(AppConstants.transactionsCollection)
          .doc(firebaseId)
          .delete();

      debugPrint('Transaction deleted from Firebase: $firebaseId');
    } catch (e) {
      debugPrint('Error deleting transaction from Firebase: $e');
      rethrow;
    }
  }

  /// Get all transactions from Firestore
  Future<List<app_transaction.Transaction>> getTransactions({
    String? accountId,
    int? limit,
  }) async {
    try {
      Query query = firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .collection(AppConstants.transactionsCollection)
          .orderBy('date', descending: true);

      if (accountId != null) {
        query = query.where('accountId', isEqualTo: accountId);
      }

      if (limit != null) {
        query = query.limit(limit);
      }

      final snapshot = await query.get();

      final transactions = snapshot.docs.map((doc) {
        return app_transaction.TransactionExtension.fromFirebaseMap(doc.data() as Map<String, dynamic>, doc.id);
      }).toList();

      debugPrint('Retrieved ${transactions.length} transactions from Firebase');
      return transactions;
    } catch (e) {
      debugPrint('Error getting transactions from Firebase: $e');
      rethrow;
    }
  }

  /// Get transactions updated after a specific timestamp
  Future<List<app_transaction.Transaction>> getTransactionsUpdatedAfter(DateTime timestamp) async {
    try {
      final snapshot = await firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .collection(AppConstants.transactionsCollection)
          .where('updatedAt', isGreaterThan: timestamp.toIso8601String())
          .orderBy('updatedAt', descending: true)
          .get();

      final transactions = snapshot.docs.map((doc) {
        return app_transaction.TransactionExtension.fromFirebaseMap(doc.data(), doc.id);
      }).toList();

      debugPrint('Retrieved ${transactions.length} updated transactions from Firebase');
      return transactions;
    } catch (e) {
      debugPrint('Error getting updated transactions from Firebase: $e');
      rethrow;
    }
  }

  /// Batch operations for efficient syncing
  Future<void> batchWrite(List<Map<String, dynamic>> operations) async {
    try {
      final batch = firestore.batch();

      for (final operation in operations) {
        final type = operation['type'] as String;
        final collection = operation['collection'] as String;
        final data = operation['data'] as Map<String, dynamic>;

        final docRef = firestore
            .collection(AppConstants.usersCollection)
            .doc(userId)
            .collection(collection);

        switch (type) {
          case 'create':
            batch.set(docRef.doc(), data);
            break;
          case 'update':
            final docId = operation['docId'] as String;
            batch.update(docRef.doc(docId), data);
            break;
          case 'delete':
            final docId = operation['docId'] as String;
            batch.delete(docRef.doc(docId));
            break;
        }
      }

      await batch.commit();
      debugPrint('Batch operation completed with ${operations.length} operations');
    } catch (e) {
      debugPrint('Error in batch operation: $e');
      rethrow;
    }
  }

  /// Check Firebase connectivity
  Future<bool> checkConnectivity() async {
    try {
      await firestore.enableNetwork();
      return true;
    } catch (e) {
      debugPrint('Firebase connectivity check failed: $e');
      return false;
    }
  }

  /// Enable offline persistence
  Future<void> enableOfflinePersistence() async {
    try {
      // Note: Persistence is now enabled via Settings in initialize()
      debugPrint('Firebase offline persistence is enabled via Settings');
    } catch (e) {
      debugPrint('Error with offline persistence: $e');
    }
  }

  /// Disable network (for testing offline functionality)
  Future<void> disableNetwork() async {
    try {
      await firestore.disableNetwork();
      debugPrint('Firebase network disabled');
    } catch (e) {
      debugPrint('Error disabling Firebase network: $e');
    }
  }

  /// Enable network
  Future<void> enableNetwork() async {
    try {
      await firestore.enableNetwork();
      debugPrint('Firebase network enabled');
    } catch (e) {
      debugPrint('Error enabling Firebase network: $e');
    }
  }
}
