import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Enum for connection types
enum ConnectionType {
  none,
  wifi,
  mobile,
  ethernet,
  bluetooth,
  vpn,
  other,
}

/// Enum for connection quality
enum ConnectionQuality {
  poor,
  fair,
  good,
  excellent,
}

/// Service for managing network connectivity
class ConnectivityService {
  static ConnectivityService? _instance;
  static ConnectivityService get instance {
    _instance ??= ConnectivityService._internal();
    return _instance!;
  }

  ConnectivityService._internal();

  final Connectivity _connectivity = Connectivity();
  final InternetConnectionChecker _connectionChecker = InternetConnectionChecker.instance;

  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  StreamSubscription<InternetConnectionStatus>? _internetSubscription;

  final StreamController<bool> _connectionStatusController = StreamController<bool>.broadcast();
  final StreamController<ConnectionType> _connectionTypeController = StreamController<ConnectionType>.broadcast();

  bool _isConnected = false;
  ConnectionType _connectionType = ConnectionType.none;
  ConnectionQuality _connectionQuality = ConnectionQuality.poor;
  SharedPreferences? _prefs;

  /// Initialize connectivity service
  Future<void> initialize() async {
    try {
      _prefs = await SharedPreferences.getInstance();

      // Configure connection checker
      _connectionChecker.checkInterval = const Duration(seconds: 10);

      // Check initial connectivity
      await _checkInitialConnectivity();

      // Listen to connectivity changes
      _connectivitySubscription = _connectivity.onConnectivityChanged.listen(_onConnectivityChanged);

      // Listen to internet connection status
      _internetSubscription = _connectionChecker.onStatusChange.listen(_onInternetStatusChanged);

      debugPrint('Connectivity service initialized successfully');
      debugPrint('Initial connection status: $_isConnected ($_connectionType)');
    } catch (e) {
      debugPrint('Error initializing connectivity service: $e');
      rethrow;
    }
  }

  /// Check initial connectivity status
  Future<void> _checkInitialConnectivity() async {
    try {
      final connectivityResults = await _connectivity.checkConnectivity();
      final hasInternet = await _connectionChecker.hasConnection;

      _updateConnectionType(connectivityResults);
      _updateConnectionStatus(hasInternet);

      await _saveConnectionStatus();
    } catch (e) {
      debugPrint('Error checking initial connectivity: $e');
      _updateConnectionStatus(false);
    }
  }

  /// Handle connectivity changes
  void _onConnectivityChanged(List<ConnectivityResult> results) {
    debugPrint('Connectivity changed: $results');
    _updateConnectionType(results);

    // Check actual internet connectivity
    _checkInternetConnection();
  }

  /// Handle internet connection status changes
  void _onInternetStatusChanged(InternetConnectionStatus status) {
    final isConnected = status == InternetConnectionStatus.connected;
    debugPrint('Internet status changed: $status (connected: $isConnected)');

    _updateConnectionStatus(isConnected);
    _saveConnectionStatus();
  }

  /// Update connection type based on connectivity results
  void _updateConnectionType(List<ConnectivityResult> results) {
    if (results.isEmpty || results.contains(ConnectivityResult.none)) {
      _connectionType = ConnectionType.none;
    } else if (results.contains(ConnectivityResult.wifi)) {
      _connectionType = ConnectionType.wifi;
    } else if (results.contains(ConnectivityResult.mobile)) {
      _connectionType = ConnectionType.mobile;
    } else if (results.contains(ConnectivityResult.ethernet)) {
      _connectionType = ConnectionType.ethernet;
    } else if (results.contains(ConnectivityResult.bluetooth)) {
      _connectionType = ConnectionType.bluetooth;
    } else if (results.contains(ConnectivityResult.vpn)) {
      _connectionType = ConnectionType.vpn;
    } else {
      _connectionType = ConnectionType.other;
    }

    _connectionTypeController.add(_connectionType);
    _updateConnectionQuality();
  }

  /// Update connection status
  void _updateConnectionStatus(bool isConnected) {
    if (_isConnected != isConnected) {
      _isConnected = isConnected;
      _connectionStatusController.add(_isConnected);

      debugPrint('Connection status updated: $_isConnected');

      // Trigger sync when connection is restored
      if (_isConnected) {
        _onConnectionRestored();
      } else {
        _onConnectionLost();
      }
    }
  }

  /// Update connection quality based on type
  void _updateConnectionQuality() {
    switch (_connectionType) {
      case ConnectionType.wifi:
      case ConnectionType.ethernet:
        _connectionQuality = ConnectionQuality.excellent;
        break;
      case ConnectionType.mobile:
        _connectionQuality = ConnectionQuality.good;
        break;
      case ConnectionType.vpn:
      case ConnectionType.other:
        _connectionQuality = ConnectionQuality.fair;
        break;
      case ConnectionType.bluetooth:
        _connectionQuality = ConnectionQuality.poor;
        break;
      case ConnectionType.none:
        _connectionQuality = ConnectionQuality.poor;
        break;
    }
  }

  /// Check internet connection manually
  Future<bool> _checkInternetConnection() async {
    try {
      final hasConnection = await _connectionChecker.hasConnection;
      _updateConnectionStatus(hasConnection);
      return hasConnection;
    } catch (e) {
      debugPrint('Error checking internet connection: $e');
      _updateConnectionStatus(false);
      return false;
    }
  }

  /// Handle connection restored
  void _onConnectionRestored() {
    debugPrint('Connection restored - triggering sync');
    // This will be handled by the sync service
  }

  /// Handle connection lost
  void _onConnectionLost() {
    debugPrint('Connection lost - switching to offline mode');
  }

  /// Save connection status to preferences
  Future<void> _saveConnectionStatus() async {
    try {
      await _prefs?.setBool('is_connected', _isConnected);
      await _prefs?.setString('connection_type', _connectionType.name);
      await _prefs?.setString('last_connection_check', DateTime.now().toIso8601String());
    } catch (e) {
      debugPrint('Error saving connection status: $e');
    }
  }



  // Public API

  /// Check if device is connected to internet
  bool get isConnected => _isConnected;

  /// Get current connection type
  ConnectionType get connectionType => _connectionType;

  /// Get current connection quality
  ConnectionQuality get connectionQuality => _connectionQuality;

  /// Check if connection is WiFi
  bool get isWiFi => _connectionType == ConnectionType.wifi;

  /// Check if connection is mobile
  bool get isMobile => _connectionType == ConnectionType.mobile;

  /// Check if connection is high quality (WiFi or Ethernet)
  bool get isHighQuality => _connectionQuality == ConnectionQuality.excellent;

  /// Stream of connection status changes
  Stream<bool> get connectionStatusStream => _connectionStatusController.stream;

  /// Stream of connection type changes
  Stream<ConnectionType> get connectionTypeStream => _connectionTypeController.stream;

  /// Manually check connectivity
  Future<bool> checkConnectivity() async {
    return await _checkInternetConnection();
  }

  /// Wait for connection to be available
  Future<bool> waitForConnection({Duration timeout = const Duration(seconds: 30)}) async {
    if (_isConnected) {
      return true;
    }

    final completer = Completer<bool>();
    late StreamSubscription subscription;

    subscription = connectionStatusStream.listen((isConnected) {
      if (isConnected) {
        subscription.cancel();
        if (!completer.isCompleted) {
          completer.complete(true);
        }
      }
    });

    // Set timeout
    Timer(timeout, () {
      subscription.cancel();
      if (!completer.isCompleted) {
        completer.complete(false);
      }
    });

    return completer.future;
  }

  /// Get connection status as string
  String getConnectionStatusString() {
    if (!_isConnected) {
      return 'Offline';
    }

    switch (_connectionType) {
      case ConnectionType.wifi:
        return 'WiFi';
      case ConnectionType.mobile:
        return 'Mobile Data';
      case ConnectionType.ethernet:
        return 'Ethernet';
      case ConnectionType.bluetooth:
        return 'Bluetooth';
      case ConnectionType.vpn:
        return 'VPN';
      case ConnectionType.other:
        return 'Connected';
      case ConnectionType.none:
        return 'Offline';
    }
  }

  /// Get connection quality as string
  String getConnectionQualityString() {
    switch (_connectionQuality) {
      case ConnectionQuality.excellent:
        return 'Excellent';
      case ConnectionQuality.good:
        return 'Good';
      case ConnectionQuality.fair:
        return 'Fair';
      case ConnectionQuality.poor:
        return 'Poor';
    }
  }

  /// Check if should sync based on connection type and user preferences
  bool shouldSync() {
    if (!_isConnected) {
      return false;
    }

    // Always sync on WiFi
    if (_connectionType == ConnectionType.wifi || _connectionType == ConnectionType.ethernet) {
      return true;
    }

    // Check user preference for mobile data sync
    final allowMobileSync = _prefs?.getBool('allow_mobile_sync') ?? true;
    if (_connectionType == ConnectionType.mobile && allowMobileSync) {
      return true;
    }

    return false;
  }

  /// Set mobile data sync preference
  Future<void> setMobileSyncEnabled(bool enabled) async {
    try {
      await _prefs?.setBool('allow_mobile_sync', enabled);
      debugPrint('Mobile sync preference updated: $enabled');
    } catch (e) {
      debugPrint('Error setting mobile sync preference: $e');
    }
  }

  /// Get mobile data sync preference
  bool get isMobileSyncEnabled => _prefs?.getBool('allow_mobile_sync') ?? true;

  /// Dispose resources
  void dispose() {
    _connectivitySubscription?.cancel();
    _internetSubscription?.cancel();
    _connectionStatusController.close();
    _connectionTypeController.close();
  }
}
