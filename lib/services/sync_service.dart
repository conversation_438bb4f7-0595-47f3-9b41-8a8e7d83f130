import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../core/constants/app_constants.dart';
import '../data/models/account.dart';
import '../data/models/transaction.dart' as app_transaction;
import '../data/database/hive_database_service.dart';
import 'firebase_service.dart';
import 'auth_service.dart';
import 'connectivity_service.dart';

/// Enum for sync status
enum SyncStatus {
  idle,
  syncing,
  success,
  error,
}

/// Sync result model
class SyncResult {
  final bool success;
  final String? error;
  final int accountsSynced;
  final int transactionsSynced;
  final DateTime timestamp;

  SyncResult({
    required this.success,
    this.error,
    required this.accountsSynced,
    required this.transactionsSynced,
    required this.timestamp,
  });
}

/// Service for managing data synchronization between local and cloud storage
class SyncService {
  static SyncService? _instance;
  static SyncService get instance {
    _instance ??= SyncService._internal();
    return _instance!;
  }

  SyncService._internal();

  final StreamController<SyncStatus> _syncStatusController = StreamController<SyncStatus>.broadcast();
  final StreamController<SyncResult> _syncResultController = StreamController<SyncResult>.broadcast();

  Timer? _periodicSyncTimer;
  SharedPreferences? _prefs;

  SyncStatus _currentStatus = SyncStatus.idle;
  DateTime? _lastSyncTime;
  bool _isInitialized = false;

  /// Initialize sync service
  Future<void> initialize() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      _lastSyncTime = _getLastSyncTime();

      // Listen to connectivity changes
      ConnectivityService.instance.connectionStatusStream.listen(_onConnectivityChanged);

      // Listen to auth state changes
      AuthService.instance.authStateChanges.listen(_onAuthStateChanged);

      // Start periodic sync if connected
      if (ConnectivityService.instance.isConnected && AuthService.instance.isSignedIn) {
        _startPeriodicSync();
      }

      _isInitialized = true;
      debugPrint('Sync service initialized successfully');

      // Perform initial sync if conditions are met
      await _performInitialSync();
    } catch (e) {
      debugPrint('Error initializing sync service: $e');
      rethrow;
    }
  }

  /// Perform initial sync on app startup
  Future<void> _performInitialSync() async {
    try {
      if (ConnectivityService.instance.isConnected &&
          AuthService.instance.isSignedIn &&
          _shouldPerformInitialSync()) {
        debugPrint('Performing initial sync...');
        await syncAll();
      }
    } catch (e) {
      debugPrint('Error during initial sync: $e');
    }
  }

  /// Check if should perform initial sync
  bool _shouldPerformInitialSync() {
    if (_lastSyncTime == null) {
      return true; // Never synced before
    }

    final timeSinceLastSync = DateTime.now().difference(_lastSyncTime!);
    return timeSinceLastSync.inHours >= 1; // Sync if more than 1 hour since last sync
  }

  /// Handle connectivity changes
  void _onConnectivityChanged(bool isConnected) {
    debugPrint('Sync service: connectivity changed to $isConnected');

    if (isConnected && AuthService.instance.isSignedIn) {
      _startPeriodicSync();
      // Trigger immediate sync when connection is restored
      _triggerSync();
    } else {
      _stopPeriodicSync();
    }
  }

  /// Handle auth state changes
  void _onAuthStateChanged(user) {
    debugPrint('Sync service: auth state changed');

    if (user != null && ConnectivityService.instance.isConnected) {
      _startPeriodicSync();
      _triggerSync();
    } else {
      _stopPeriodicSync();
    }
  }

  /// Start periodic sync timer
  void _startPeriodicSync() {
    if (_periodicSyncTimer?.isActive == true) {
      return; // Already running
    }

    _periodicSyncTimer = Timer.periodic(
      const Duration(minutes: 5), // Sync every 5 minutes
      (_) => _triggerSync(),
    );

    debugPrint('Periodic sync started');
  }

  /// Stop periodic sync timer
  void _stopPeriodicSync() {
    _periodicSyncTimer?.cancel();
    _periodicSyncTimer = null;
    debugPrint('Periodic sync stopped');
  }

  /// Trigger sync if conditions are met
  void _triggerSync() {
    if (_canSync()) {
      syncAll().catchError((error) {
        debugPrint('Background sync failed: $error');
        return SyncResult(
          success: false,
          error: error.toString(),
          accountsSynced: 0,
          transactionsSynced: 0,
          timestamp: DateTime.now(),
        );
      });
    }
  }

  /// Check if sync can be performed
  bool _canSync() {
    return _isInitialized &&
           _currentStatus != SyncStatus.syncing &&
           ConnectivityService.instance.isConnected &&
           ConnectivityService.instance.shouldSync() &&
           AuthService.instance.isSignedIn;
  }

  /// Sync all data (accounts and transactions)
  Future<SyncResult> syncAll() async {
    if (!_canSync()) {
      final error = 'Cannot sync: ${_getSyncBlockReason()}';
      debugPrint(error);
      return SyncResult(
        success: false,
        error: error,
        accountsSynced: 0,
        transactionsSynced: 0,
        timestamp: DateTime.now(),
      );
    }

    _updateSyncStatus(SyncStatus.syncing);

    try {
      debugPrint('Starting full sync...');

      int accountsSynced = 0;
      int transactionsSynced = 0;

      // Sync accounts
      accountsSynced = await _syncAccounts();

      // Sync transactions
      transactionsSynced = await _syncTransactions();

      // Update last sync time
      await _updateLastSyncTime();

      final result = SyncResult(
        success: true,
        accountsSynced: accountsSynced,
        transactionsSynced: transactionsSynced,
        timestamp: DateTime.now(),
      );

      _updateSyncStatus(SyncStatus.success);
      _syncResultController.add(result);

      debugPrint('Sync completed successfully: $accountsSynced accounts, $transactionsSynced transactions');
      return result;

    } catch (e) {
      debugPrint('Sync failed: $e');

      final result = SyncResult(
        success: false,
        error: e.toString(),
        accountsSynced: 0,
        transactionsSynced: 0,
        timestamp: DateTime.now(),
      );

      _updateSyncStatus(SyncStatus.error);
      _syncResultController.add(result);

      return result;
    }
  }

  /// Sync accounts between local and cloud
  Future<int> _syncAccounts() async {
    try {
      int syncedCount = 0;

      // 1. Upload unsynced local accounts to cloud
      final unsyncedAccounts = await HiveDatabaseService.instance.getUnsyncedAccounts();
      for (final account in unsyncedAccounts) {
        try {
          final firebaseId = await FirebaseService.instance.saveAccount(account);
          final syncedAccount = account.markAsSynced(firebaseId: firebaseId);
          await HiveDatabaseService.instance.saveAccount(syncedAccount);
          syncedCount++;
          debugPrint('Uploaded account to cloud: ${account.id}');
        } catch (e) {
          debugPrint('Failed to upload account ${account.id}: $e');
        }
      }

      // 2. Download updated accounts from cloud
      final lastSync = _lastSyncTime ?? DateTime.fromMillisecondsSinceEpoch(0);
      final updatedAccounts = await FirebaseService.instance.getAccountsUpdatedAfter(lastSync);

      for (final cloudAccount in updatedAccounts) {
        try {
          final localAccount = await HiveDatabaseService.instance.getAccount(cloudAccount.id);

          if (localAccount == null) {
            // New account from cloud
            await HiveDatabaseService.instance.saveAccount(cloudAccount);
            syncedCount++;
            debugPrint('Downloaded new account from cloud: ${cloudAccount.id}');
          } else {
            // Check for conflicts and resolve
            final resolvedAccount = _resolveAccountConflict(localAccount, cloudAccount);
            if (resolvedAccount.id != localAccount.id) {
              await HiveDatabaseService.instance.saveAccount(resolvedAccount);
              syncedCount++;
              debugPrint('Resolved account conflict: ${cloudAccount.id}');
            }
          }
        } catch (e) {
          debugPrint('Failed to process cloud account ${cloudAccount.id}: $e');
        }
      }

      return syncedCount;
    } catch (e) {
      debugPrint('Error syncing accounts: $e');
      rethrow;
    }
  }

  /// Sync transactions between local and cloud
  Future<int> _syncTransactions() async {
    try {
      int syncedCount = 0;

      // 1. Upload unsynced local transactions to cloud
      final unsyncedTransactions = await HiveDatabaseService.instance.getUnsyncedTransactions();
      for (final transaction in unsyncedTransactions) {
        try {
          final firebaseId = await FirebaseService.instance.saveTransaction(transaction);
          final syncedTransaction = transaction.markAsSynced(firebaseId: firebaseId);
          await HiveDatabaseService.instance.saveTransaction(syncedTransaction);
          syncedCount++;
          debugPrint('Uploaded transaction to cloud: ${transaction.id}');
        } catch (e) {
          debugPrint('Failed to upload transaction ${transaction.id}: $e');
        }
      }

      // 2. Download updated transactions from cloud
      final lastSync = _lastSyncTime ?? DateTime.fromMillisecondsSinceEpoch(0);
      final updatedTransactions = await FirebaseService.instance.getTransactionsUpdatedAfter(lastSync);

      for (final cloudTransaction in updatedTransactions) {
        try {
          final localTransaction = await HiveDatabaseService.instance.getTransaction(cloudTransaction.id);

          if (localTransaction == null) {
            // New transaction from cloud
            await HiveDatabaseService.instance.saveTransaction(cloudTransaction);
            syncedCount++;
            debugPrint('Downloaded new transaction from cloud: ${cloudTransaction.id}');
          } else {
            // Check for conflicts and resolve
            final resolvedTransaction = _resolveTransactionConflict(localTransaction, cloudTransaction);
            if (resolvedTransaction.id != localTransaction.id) {
              await HiveDatabaseService.instance.saveTransaction(resolvedTransaction);
              syncedCount++;
              debugPrint('Resolved transaction conflict: ${cloudTransaction.id}');
            }
          }
        } catch (e) {
          debugPrint('Failed to process cloud transaction ${cloudTransaction.id}: $e');
        }
      }

      return syncedCount;
    } catch (e) {
      debugPrint('Error syncing transactions: $e');
      rethrow;
    }
  }

  /// Resolve account conflict using last-write-wins strategy
  Account _resolveAccountConflict(Account local, Account cloud) {
    // Last-write-wins based on updatedAt timestamp
    if (cloud.updatedAt.isAfter(local.updatedAt)) {
      debugPrint('Cloud account is newer, using cloud version: ${cloud.id}');
      return cloud;
    } else {
      debugPrint('Local account is newer, keeping local version: ${local.id}');
      return local;
    }
  }

  /// Resolve transaction conflict using last-write-wins strategy
  app_transaction.Transaction _resolveTransactionConflict(app_transaction.Transaction local, app_transaction.Transaction cloud) {
    // Last-write-wins based on updatedAt timestamp
    if (cloud.updatedAt.isAfter(local.updatedAt)) {
      debugPrint('Cloud transaction is newer, using cloud version: ${cloud.id}');
      return cloud;
    } else {
      debugPrint('Local transaction is newer, keeping local version: ${local.id}');
      return local;
    }
  }

  /// Update sync status and notify listeners
  void _updateSyncStatus(SyncStatus status) {
    _currentStatus = status;
    _syncStatusController.add(status);
  }

  /// Get reason why sync is blocked
  String _getSyncBlockReason() {
    if (!_isInitialized) return 'Service not initialized';
    if (_currentStatus == SyncStatus.syncing) return 'Already syncing';
    if (!ConnectivityService.instance.isConnected) return 'No internet connection';
    if (!ConnectivityService.instance.shouldSync()) return 'Connection not suitable for sync';
    if (!AuthService.instance.isSignedIn) return 'User not authenticated';
    return 'Unknown reason';
  }

  /// Update last sync time
  Future<void> _updateLastSyncTime() async {
    try {
      _lastSyncTime = DateTime.now();
      await _prefs?.setString(AppConstants.keyLastSyncTime, _lastSyncTime!.toIso8601String());
    } catch (e) {
      debugPrint('Error updating last sync time: $e');
    }
  }

  /// Get last sync time from preferences
  DateTime? _getLastSyncTime() {
    try {
      final timeString = _prefs?.getString(AppConstants.keyLastSyncTime);
      if (timeString != null) {
        return DateTime.parse(timeString);
      }
    } catch (e) {
      debugPrint('Error getting last sync time: $e');
    }
    return null;
  }

  // Public API

  /// Get current sync status
  SyncStatus get currentStatus => _currentStatus;

  /// Get last sync time
  DateTime? get lastSyncTime => _lastSyncTime;

  /// Stream of sync status changes
  Stream<SyncStatus> get syncStatusStream => _syncStatusController.stream;

  /// Stream of sync results
  Stream<SyncResult> get syncResultStream => _syncResultController.stream;

  /// Check if currently syncing
  bool get isSyncing => _currentStatus == SyncStatus.syncing;

  /// Manually trigger sync
  Future<SyncResult> manualSync() async {
    debugPrint('Manual sync triggered');
    return await syncAll();
  }

  /// Get pending sync count
  Future<int> getPendingSyncCount() async {
    try {
      final unsyncedAccounts = await HiveDatabaseService.instance.getUnsyncedAccounts();
      final unsyncedTransactions = await HiveDatabaseService.instance.getUnsyncedTransactions();
      return unsyncedAccounts.length + unsyncedTransactions.length;
    } catch (e) {
      debugPrint('Error getting pending sync count: $e');
      return 0;
    }
  }

  /// Force sync even if conditions are not met (for testing)
  Future<SyncResult> forceSync() async {
    _updateSyncStatus(SyncStatus.syncing);
    return await syncAll();
  }

  /// Dispose resources
  void dispose() {
    _stopPeriodicSync();
    _syncStatusController.close();
    _syncResultController.close();
  }
}
