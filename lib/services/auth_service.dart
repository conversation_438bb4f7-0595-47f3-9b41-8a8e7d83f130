import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../core/constants/app_constants.dart';
import 'firebase_service.dart';

/// Service for managing Firebase Authentication (Anonymous)
class AuthService {
  static AuthService? _instance;
  static AuthService get instance {
    _instance ??= AuthService._internal();
    return _instance!;
  }

  AuthService._internal();

  FirebaseAuth? _auth;
  User? _currentUser;
  SharedPreferences? _prefs;

  /// Initialize authentication service
  Future<void> initialize() async {
    try {
      _auth = FirebaseAuth.instance;
      _prefs = await SharedPreferences.getInstance();
      
      // Listen to auth state changes
      _auth!.authStateChanges().listen(_onAuthStateChanged);
      
      // Check if user is already signed in
      _currentUser = _auth!.currentUser;
      
      if (_currentUser != null) {
        debugPrint('User already signed in: ${_currentUser!.uid}');
        FirebaseService.instance.setUserId(_currentUser!.uid);
        await _saveUserId(_currentUser!.uid);
      } else {
        // Try to get saved user ID
        final savedUserId = _getSavedUserId();
        if (savedUserId != null) {
          debugPrint('Found saved user ID, attempting to restore session');
          // The auth state listener will handle the rest
        } else {
          // Sign in anonymously
          await signInAnonymously();
        }
      }
      
      debugPrint('Auth service initialized successfully');
    } catch (e) {
      debugPrint('Error initializing auth service: $e');
      rethrow;
    }
  }

  /// Get Firebase Auth instance
  FirebaseAuth get auth {
    if (_auth == null) {
      throw Exception('Auth service not initialized');
    }
    return _auth!;
  }

  /// Get current user
  User? get currentUser => _currentUser;

  /// Get current user ID
  String? get currentUserId => _currentUser?.uid;

  /// Check if user is signed in
  bool get isSignedIn => _currentUser != null;

  /// Sign in anonymously
  Future<User?> signInAnonymously() async {
    try {
      debugPrint('Attempting anonymous sign in...');
      
      final userCredential = await _auth!.signInAnonymously();
      final user = userCredential.user;
      
      if (user != null) {
        debugPrint('Anonymous sign in successful: ${user.uid}');
        _currentUser = user;
        FirebaseService.instance.setUserId(user.uid);
        await _saveUserId(user.uid);
        return user;
      } else {
        debugPrint('Anonymous sign in failed: user is null');
        return null;
      }
    } catch (e) {
      debugPrint('Error during anonymous sign in: $e');
      rethrow;
    }
  }

  /// Sign out
  Future<void> signOut() async {
    try {
      await _auth!.signOut();
      _currentUser = null;
      await _clearSavedUserId();
      debugPrint('User signed out successfully');
    } catch (e) {
      debugPrint('Error during sign out: $e');
      rethrow;
    }
  }

  /// Delete current user account
  Future<void> deleteAccount() async {
    try {
      if (_currentUser != null) {
        await _currentUser!.delete();
        _currentUser = null;
        await _clearSavedUserId();
        debugPrint('User account deleted successfully');
      }
    } catch (e) {
      debugPrint('Error deleting user account: $e');
      rethrow;
    }
  }

  /// Handle authentication state changes
  void _onAuthStateChanged(User? user) {
    debugPrint('Auth state changed: ${user?.uid ?? 'null'}');
    
    _currentUser = user;
    
    if (user != null) {
      FirebaseService.instance.setUserId(user.uid);
      _saveUserId(user.uid);
    } else {
      _clearSavedUserId();
    }
  }

  /// Save user ID to local storage
  Future<void> _saveUserId(String userId) async {
    try {
      await _prefs?.setString(AppConstants.keyUserId, userId);
      debugPrint('User ID saved to local storage: $userId');
    } catch (e) {
      debugPrint('Error saving user ID: $e');
    }
  }

  /// Get saved user ID from local storage
  String? _getSavedUserId() {
    try {
      final userId = _prefs?.getString(AppConstants.keyUserId);
      if (userId != null) {
        debugPrint('Retrieved saved user ID: $userId');
      }
      return userId;
    } catch (e) {
      debugPrint('Error getting saved user ID: $e');
      return null;
    }
  }

  /// Clear saved user ID from local storage
  Future<void> _clearSavedUserId() async {
    try {
      await _prefs?.remove(AppConstants.keyUserId);
      debugPrint('Saved user ID cleared from local storage');
    } catch (e) {
      debugPrint('Error clearing saved user ID: $e');
    }
  }

  /// Get authentication token for API calls
  Future<String?> getIdToken({bool forceRefresh = false}) async {
    try {
      if (_currentUser != null) {
        return await _currentUser!.getIdToken(forceRefresh);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting ID token: $e');
      return null;
    }
  }

  /// Refresh authentication token
  Future<void> refreshToken() async {
    try {
      if (_currentUser != null) {
        await _currentUser!.getIdToken(true);
        debugPrint('Authentication token refreshed');
      }
    } catch (e) {
      debugPrint('Error refreshing token: $e');
    }
  }

  /// Check if authentication is valid
  Future<bool> isAuthValid() async {
    try {
      if (_currentUser == null) {
        return false;
      }
      
      // Try to get a fresh token
      final token = await getIdToken(forceRefresh: true);
      return token != null;
    } catch (e) {
      debugPrint('Auth validation failed: $e');
      return false;
    }
  }

  /// Ensure user is authenticated (sign in if needed)
  Future<bool> ensureAuthenticated() async {
    try {
      if (isSignedIn) {
        // Check if current auth is still valid
        final isValid = await isAuthValid();
        if (isValid) {
          return true;
        }
      }
      
      // Sign in anonymously if not authenticated or auth is invalid
      final user = await signInAnonymously();
      return user != null;
    } catch (e) {
      debugPrint('Error ensuring authentication: $e');
      return false;
    }
  }

  /// Get user metadata
  Map<String, dynamic> getUserMetadata() {
    if (_currentUser == null) {
      return {};
    }
    
    return {
      'uid': _currentUser!.uid,
      'isAnonymous': _currentUser!.isAnonymous,
      'creationTime': _currentUser!.metadata.creationTime?.toIso8601String(),
      'lastSignInTime': _currentUser!.metadata.lastSignInTime?.toIso8601String(),
    };
  }

  /// Handle authentication errors
  String getAuthErrorMessage(dynamic error) {
    if (error is FirebaseAuthException) {
      switch (error.code) {
        case 'network-request-failed':
          return 'Network error. Please check your connection and try again.';
        case 'too-many-requests':
          return 'Too many requests. Please try again later.';
        case 'user-disabled':
          return 'This account has been disabled.';
        case 'operation-not-allowed':
          return 'Anonymous authentication is not enabled.';
        default:
          return 'Authentication error. Please try again.';
      }
    }
    
    return 'An unexpected error occurred. Please try again.';
  }

  /// Listen to authentication state changes
  Stream<User?> get authStateChanges => _auth!.authStateChanges();

  /// Check if running in offline mode
  bool get isOfflineMode {
    return !isSignedIn;
  }

  /// Force offline mode (for testing)
  Future<void> enableOfflineMode() async {
    try {
      await signOut();
      debugPrint('Offline mode enabled');
    } catch (e) {
      debugPrint('Error enabling offline mode: $e');
    }
  }

  /// Re-enable online mode
  Future<void> enableOnlineMode() async {
    try {
      if (!isSignedIn) {
        await signInAnonymously();
        debugPrint('Online mode enabled');
      }
    } catch (e) {
      debugPrint('Error enabling online mode: $e');
    }
  }
}
