import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'core/theme/app_theme.dart';
import 'core/constants/app_constants.dart';
import 'presentation/screens/main_screen.dart';
import 'data/database/hive_database_service.dart';
import 'services/firebase_service.dart';
import 'services/auth_service.dart';
import 'services/connectivity_service.dart';
import 'services/sync_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // Initialize Firebase with options
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    debugPrint('Firebase initialized successfully');

    // Initialize Hive database
    await HiveDatabaseService.instance.initialize();

    // Initialize services in order
    await FirebaseService.instance.initialize();
    await AuthService.instance.initialize();
    await ConnectivityService.instance.initialize();
    await SyncService.instance.initialize();

    debugPrint('All services initialized successfully');
  } catch (e) {
    debugPrint('Error during initialization: $e');
    // Continue with offline-only mode
    try {
      await HiveDatabaseService.instance.initialize();
      debugPrint('Offline mode initialized successfully');
    } catch (offlineError) {
      debugPrint('Critical error: Failed to initialize offline mode: $offlineError');
    }
  }

  runApp(const ProviderScope(child: ExpenseTrackerApp()));
}

class ExpenseTrackerApp extends StatelessWidget {
  const ExpenseTrackerApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: AppConstants.appName,
      theme: AppTheme.lightTheme,
      home: const MainScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}


