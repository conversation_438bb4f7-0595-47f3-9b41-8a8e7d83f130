// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBKDEGLMq2JXBcCvCmPsxuIAOwdKCmv9ss',
    appId: '1:1005453638355:web:1be57522d5073946ad7226',
    messagingSenderId: '1005453638355',
    projectId: 'expense-tracker-b3f2b',
    authDomain: 'expense-tracker-b3f2b.firebaseapp.com',
    storageBucket: 'expense-tracker-b3f2b.firebasestorage.app',
    measurementId: 'G-QRNC1S5B70',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDllWg33zSVQdpETcGrKm6kNWBlV1PobGI',
    appId: '1:1005453638355:android:17d5087a06b458ecad7226',
    messagingSenderId: '1005453638355',
    projectId: 'expense-tracker-b3f2b',
    storageBucket: 'expense-tracker-b3f2b.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBNvOg9jUGgL5KHeDZ6h1rqJC1NXizDVUs',
    appId: '1:1005453638355:ios:74f4a765db590c49ad7226',
    messagingSenderId: '1005453638355',
    projectId: 'expense-tracker-b3f2b',
    storageBucket: 'expense-tracker-b3f2b.firebasestorage.app',
    iosBundleId: 'com.expensetracker',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'demo-api-key',
    appId: '1:123456789:macos:demo',
    messagingSenderId: '123456789',
    projectId: 'expense-tracker-demo',
    storageBucket: 'expense-tracker-demo.appspot.com',
    iosBundleId: 'com.expensetracker',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'demo-api-key',
    appId: '1:123456789:windows:demo',
    messagingSenderId: '123456789',
    projectId: 'expense-tracker-demo',
    storageBucket: 'expense-tracker-demo.appspot.com',
  );
}