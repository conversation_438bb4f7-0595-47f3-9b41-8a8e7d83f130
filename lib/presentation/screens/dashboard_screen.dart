import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../widgets/total_balance_card.dart';
import '../widgets/accounts_overview_section.dart';
import '../widgets/recent_transactions_section.dart';
import '../widgets/quick_actions_section.dart';
import '../widgets/sync_status_indicator.dart';
import '../widgets/analytics_cards.dart';
import '../../core/constants/app_constants.dart';
import '../../core/utils/extensions.dart';
import '../../providers/account_providers.dart';
import '../../providers/transaction_providers.dart';
import '../../providers/analytics_providers.dart';

class DashboardScreen extends ConsumerWidget {
  const DashboardScreen({super.key, this.onNavigateToTab});

  final void Function(int)? onNavigateToTab;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Dashboard'),
        elevation: 0,
        backgroundColor: Colors.transparent,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              // Refresh all data
              ref.invalidate(totalBalanceNotifierProvider);
              ref.invalidate(accountBalanceNotifierProvider);
              ref.invalidate(recentTransactionNotifierProvider);
            },
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          // Refresh all data
          ref.invalidate(totalBalanceNotifierProvider);
          ref.invalidate(accountBalanceNotifierProvider);
          ref.invalidate(recentTransactionNotifierProvider);
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Sync Status Indicator
              const SyncStatusIndicator(),

              const SizedBox(height: 16),

              // Total Balance Card
              const TotalBalanceCard(),

              const SizedBox(height: 24),

              // Quick Actions
              const QuickActionsSection(),

              const SizedBox(height: 24),

              // Monthly Analytics
              SpendingOverviewCard(month: DateTime.now()),

              const SizedBox(height: 24),

              // Smart Insights
              const SpendingInsightsCard(),

              const SizedBox(height: 24),

              // Category Spending Chart
              CategorySpendingChartCard(month: DateTime.now()),

              const SizedBox(height: 24),

              // Weekly Trend
              WeeklySpendingTrendCard(
                startDate: DateTime.now().subtract(const Duration(days: 6)),
              ),

              const SizedBox(height: 24),

              // Accounts Overview
              Text(
                'Accounts',
                style: context.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 12),
              SizedBox(
                height: 160, // Increased height for modern card design
                child: const AccountsOverviewSection(),
              ),

              const SizedBox(height: 24),

              // Recent Transactions
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Recent Transactions',
                    style: context.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      // Navigate to Transactions tab (index 2)
                      onNavigateToTab?.call(2);
                    },
                    child: const Text('View All'),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              const RecentTransactionsSection(),

              const SizedBox(height: 100), // Space for FAB
            ],
          ),
        ),
      ),
    );
  }
}
