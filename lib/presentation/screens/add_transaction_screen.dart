import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../core/theme/app_theme.dart';
import '../../core/utils/extensions.dart';
import '../../data/models/transaction.dart';
import '../../data/models/account.dart';
import '../../data/models/account_balance.dart';
import '../../providers/transaction_providers.dart';
import '../../providers/account_providers.dart';
import 'add_account_screen.dart';

class AddTransactionScreen extends ConsumerStatefulWidget {
  final String? initialType; // 'income' or 'expense'

  const AddTransactionScreen({
    super.key,
    this.initialType,
  });

  @override
  ConsumerState<AddTransactionScreen> createState() => _AddTransactionScreenState();
}

class _AddTransactionScreenState extends ConsumerState<AddTransactionScreen> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _noteController = TextEditingController();

  TransactionType _selectedType = TransactionType.expense;
  Account? _selectedAccount;
  DateTime _selectedDate = DateTime.now();
  bool _isLoading = false;

  // Smart features
  List<String> _recentDescriptions = [];
  Map<String, double> _amountMemory = {};
  Map<String, String> _lastUsedAccounts = {};
  List<AccountBalance> _accountBalances = [];
  bool _showInsufficientFundsWarning = false;

  @override
  void initState() {
    super.initState();

    // Set initial type if provided
    if (widget.initialType == 'income') {
      _selectedType = TransactionType.income;
    } else if (widget.initialType == 'expense') {
      _selectedType = TransactionType.expense;
    }

    // Load smart features
    _loadSmartFeatures();

    // Add listeners for smart features
    _descriptionController.addListener(_onDescriptionChanged);
    _amountController.addListener(_onAmountChanged);
  }

  /// Load smart features data
  Future<void> _loadSmartFeatures() async {
    await _loadRecentDescriptions();
    await _loadAmountMemory();
    await _loadLastUsedAccounts();
    await _loadAccountBalances();
    _loadLastUsedAccount();
  }

  /// Load recent descriptions for suggestions
  Future<void> _loadRecentDescriptions() async {
    try {
      // This will be loaded from the provider
      final recentDescriptions = await ref.read(recentDescriptionsProvider.future);
      if (mounted) {
        setState(() {
          _recentDescriptions = recentDescriptions;
        });
      }
    } catch (e) {
      debugPrint('Error loading recent descriptions: $e');
    }
  }

  /// Load amount memory from preferences
  Future<void> _loadAmountMemory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final amountMemoryJson = prefs.getString('amount_memory') ?? '{}';
      final Map<String, dynamic> decoded = Map<String, dynamic>.from(
        Uri.splitQueryString(amountMemoryJson.replaceAll('{', '').replaceAll('}', ''))
      );

      _amountMemory = decoded.map((key, value) => MapEntry(key, double.tryParse(value.toString()) ?? 0.0));
    } catch (e) {
      debugPrint('Error loading amount memory: $e');
      _amountMemory = {};
    }
  }

  /// Load last used accounts from preferences
  Future<void> _loadLastUsedAccounts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final incomeAccount = prefs.getString('last_income_account');
      final expenseAccount = prefs.getString('last_expense_account');

      _lastUsedAccounts = {
        'income': incomeAccount ?? '',
        'expense': expenseAccount ?? '',
      };
    } catch (e) {
      debugPrint('Error loading last used accounts: $e');
      _lastUsedAccounts = {};
    }
  }

  /// Load account balances
  Future<void> _loadAccountBalances() async {
    try {
      final accountBalances = await ref.read(accountsWithBalancesProvider.future);
      if (mounted) {
        setState(() {
          _accountBalances = accountBalances;
        });
      }
    } catch (e) {
      debugPrint('Error loading account balances: $e');
    }
  }

  /// Load last used account for current transaction type
  void _loadLastUsedAccount() {
    final lastAccountId = _lastUsedAccounts[_selectedType.name];
    if (lastAccountId != null && lastAccountId.isNotEmpty) {
      final account = _accountBalances
          .map((ab) => ab.account)
          .where((account) => account.id == lastAccountId)
          .firstOrNull;
      if (account != null) {
        setState(() {
          _selectedAccount = account;
        });
      }
    }
  }

  /// Handle description changes for smart suggestions
  void _onDescriptionChanged() {
    final description = _descriptionController.text.trim();
    if (description.isNotEmpty && _amountMemory.containsKey(description)) {
      final suggestedAmount = _amountMemory[description]!;
      if (_amountController.text.isEmpty) {
        _amountController.text = suggestedAmount.toString();
      }
    }
  }

  /// Handle amount changes for insufficient funds warning
  void _onAmountChanged() {
    _checkInsufficientFunds();
  }

  /// Check for insufficient funds warning
  void _checkInsufficientFunds() {
    if (_selectedType == TransactionType.expense &&
        _selectedAccount != null &&
        _amountController.text.isNotEmpty) {

      final amount = double.tryParse(_amountController.text) ?? 0.0;
      final accountBalance = _accountBalances
          .where((ab) => ab.account.id == _selectedAccount!.id)
          .firstOrNull;

      if (accountBalance != null) {
        final willBeNegative = accountBalance.currentBalance - amount < 0;
        setState(() {
          _showInsufficientFundsWarning = willBeNegative;
        });
      }
    } else {
      setState(() {
        _showInsufficientFundsWarning = false;
      });
    }
  }

  /// Save smart features data
  Future<void> _saveSmartFeatures() async {
    await _saveAmountMemory();
    await _saveLastUsedAccount();
  }

  /// Save amount memory to preferences
  Future<void> _saveAmountMemory() async {
    try {
      final description = _descriptionController.text.trim();
      final amount = double.tryParse(_amountController.text);

      if (description.isNotEmpty && amount != null && amount > 0) {
        _amountMemory[description] = amount;

        final prefs = await SharedPreferences.getInstance();
        final amountMemoryString = _amountMemory.entries
            .map((e) => '${e.key}=${e.value}')
            .join('&');
        await prefs.setString('amount_memory', '{$amountMemoryString}');
      }
    } catch (e) {
      debugPrint('Error saving amount memory: $e');
    }
  }

  /// Save last used account to preferences
  Future<void> _saveLastUsedAccount() async {
    try {
      if (_selectedAccount != null) {
        final prefs = await SharedPreferences.getInstance();
        final key = 'last_${_selectedType.name}_account';
        await prefs.setString(key, _selectedAccount!.id);
      }
    } catch (e) {
      debugPrint('Error saving last used account: $e');
    }
  }

  @override
  void dispose() {
    _amountController.removeListener(_onAmountChanged);
    _descriptionController.removeListener(_onDescriptionChanged);
    _amountController.dispose();
    _descriptionController.dispose();
    _noteController.dispose();
    super.dispose();
  }

  Future<void> _createTransaction() async {
    if (!_formKey.currentState!.validate()) return;
    if (_selectedAccount == null) {
      context.showError('Please select an account');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final amount = double.parse(_amountController.text);
      final description = _descriptionController.text.trim();
      final note = _noteController.text.trim().isNotEmpty
          ? _noteController.text.trim()
          : null;

      await ref.read(transactionNotifierProvider.notifier).createTransaction(
        accountId: _selectedAccount!.id,
        type: _selectedType,
        amount: amount,
        description: description,
        date: _selectedDate,
        note: note,
      );

      // Save smart features data
      await _saveSmartFeatures();

      if (mounted) {
        final navigator = Navigator.of(context);
        context.showSuccess('Transaction added successfully');
        navigator.pop();

        // Refresh related data
        ref.read(accountBalanceNotifierProvider.notifier).refresh();
        ref.read(totalBalanceNotifierProvider.notifier).refresh();
        ref.read(recentTransactionNotifierProvider.notifier).refresh();
      }
    } catch (e) {
      if (mounted) {
        context.showError('Failed to add transaction. Please try again.');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _selectDate() async {
    if (!mounted) return;

    final picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (picked != null && mounted) {
      final time = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(_selectedDate),
      );

      if (mounted) {
        if (time != null) {
          setState(() {
            _selectedDate = DateTime(
              picked.year,
              picked.month,
              picked.day,
              time.hour,
              time.minute,
            );
          });
        } else {
          setState(() {
            _selectedDate = DateTime(
              picked.year,
              picked.month,
              picked.day,
              _selectedDate.hour,
              _selectedDate.minute,
            );
          });
        }
      }
    }
  }

  void _showAddAccountModal() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: const AddAccountScreen(),
      ),
    ).then((_) {
      // Refresh accounts after modal closes
      ref.invalidate(accountsProvider);
    });
  }

  @override
  Widget build(BuildContext context) {
    final accountsAsync = ref.watch(accountsProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text('Add ${_selectedType.displayName}'),
        elevation: 0,
        backgroundColor: Colors.transparent,
        leading: IconButton(
          icon: const Icon(Icons.close),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Transaction Type Toggle
              _buildTypeToggle(),

              const SizedBox(height: 24),

              // Amount Field
              Text(
                'Amount',
                style: AppTheme.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              TextFormField(
                controller: _amountController,
                decoration: const InputDecoration(
                  hintText: '0.00',
                  prefixIcon: Icon(Icons.attach_money),
                ),
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
                ],
                autofocus: true,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Amount is required';
                  }
                  final amount = double.tryParse(value);
                  if (amount == null || amount <= 0) {
                    return 'Please enter a valid amount greater than zero';
                  }
                  if (amount > 999999.99) {
                    return 'Amount must be less than \$999,999.99';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 24),

              // Description Field with Smart Suggestions
              Text(
                'Description',
                style: AppTheme.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TextFormField(
                    controller: _descriptionController,
                    decoration: const InputDecoration(
                      hintText: 'What was this for?',
                      prefixIcon: Icon(Icons.description),
                    ),
                    textCapitalization: TextCapitalization.sentences,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Description is required';
                      }
                      if (value.trim().length > 100) {
                        return 'Description must be 100 characters or less';
                      }
                      return null;
                    },
                  ),
                  if (_recentDescriptions.isNotEmpty) ...[
                    const SizedBox(height: 8),
                    Text(
                      'Recent descriptions:',
                      style: AppTheme.bodySmall.copyWith(
                        color: AppTheme.textSecondary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Wrap(
                      spacing: 8,
                      runSpacing: 4,
                      children: _recentDescriptions.take(5).map((description) {
                        return GestureDetector(
                          onTap: () {
                            _descriptionController.text = description;
                            _onDescriptionChanged(); // Trigger amount suggestion
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: AppTheme.primaryColor.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: AppTheme.primaryColor.withValues(alpha: 0.3),
                              ),
                            ),
                            child: Text(
                              description,
                              style: AppTheme.bodySmall.copyWith(
                                color: AppTheme.primaryColor,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ],
                ],
              ),

              const SizedBox(height: 24),

              // Account Selection
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Account',
                    style: AppTheme.titleMedium.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  TextButton.icon(
                    onPressed: _showAddAccountModal,
                    icon: const Icon(Icons.add, size: 16),
                    label: const Text('Create Account'),
                    style: TextButton.styleFrom(
                      foregroundColor: AppTheme.primaryColor,
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  accountsAsync.when(
                    data: (accounts) => _buildAccountDropdown(accounts),
                    loading: () => _buildLoadingDropdown(),
                    error: (_, __) => _buildErrorDropdown(),
                  ),
                  if (_showInsufficientFundsWarning) ...[
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: AppTheme.warningColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: AppTheme.warningColor.withValues(alpha: 0.3),
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.warning_amber,
                            color: AppTheme.warningColor,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'This transaction will make your account balance negative.',
                              style: AppTheme.bodySmall.copyWith(
                                color: AppTheme.warningColor,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),

              const SizedBox(height: 24),

              // Date Selection
              Text(
                'Date & Time',
                style: AppTheme.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              InkWell(
                onTap: _selectDate,
                borderRadius: BorderRadius.circular(8),
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey[300]!),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.calendar_today, color: Colors.grey),
                      const SizedBox(width: 12),
                      Text(
                        _selectedDate.toDateTimeString(),
                        style: AppTheme.bodyLarge,
                      ),
                      const Spacer(),
                      const Icon(Icons.arrow_drop_down, color: Colors.grey),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Note Field (Optional)
              Text(
                'Note (Optional)',
                style: AppTheme.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              TextFormField(
                controller: _noteController,
                decoration: const InputDecoration(
                  hintText: 'Add a note...',
                  prefixIcon: Icon(Icons.note),
                ),
                textCapitalization: TextCapitalization.sentences,
                maxLines: 3,
                maxLength: 200,
              ),

              const SizedBox(height: 40),

              // Add Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _createTransaction,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _selectedType.isIncome
                        ? AppTheme.incomeColor
                        : AppTheme.expenseColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: _isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(
                        'Add ${_selectedType.displayName}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTypeToggle() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildTypeOption(TransactionType.income),
          ),
          Expanded(
            child: _buildTypeOption(TransactionType.expense),
          ),
        ],
      ),
    );
  }

  Widget _buildTypeOption(TransactionType type) {
    final isSelected = _selectedType == type;
    final color = type.isIncome ? AppTheme.incomeColor : AppTheme.expenseColor;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedType = type;
        });
        // Load last used account for new type
        _loadLastUsedAccount();
        // Check insufficient funds for new type
        _checkInsufficientFunds();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          color: isSelected ? color : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              type.isIncome ? Icons.add_circle : Icons.remove_circle,
              color: isSelected ? Colors.white : color,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              type.displayName,
              style: AppTheme.titleMedium.copyWith(
                color: isSelected ? Colors.white : color,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountDropdown(List<Account> accounts) {
    if (accounts.isEmpty) {
      return GestureDetector(
        onTap: _showAddAccountModal,
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: Border.all(color: AppTheme.primaryColor.withValues(alpha: 0.3)),
            borderRadius: BorderRadius.circular(8),
            color: AppTheme.primaryColor.withValues(alpha: 0.05),
          ),
          child: Row(
            children: [
              Icon(
                Icons.add_circle_outline,
                color: AppTheme.primaryColor,
                size: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Create your first account',
                      style: AppTheme.bodyMedium.copyWith(
                        color: AppTheme.primaryColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      'Tap here to add an account to get started',
                      style: AppTheme.bodySmall.copyWith(
                        color: AppTheme.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                color: AppTheme.primaryColor,
                size: 16,
              ),
            ],
          ),
        ),
      );
    }

    return DropdownButtonFormField<Account>(
      value: _selectedAccount,
      decoration: const InputDecoration(
        prefixIcon: Icon(Icons.account_balance_wallet),
      ),
      hint: const Text('Select an account'),
      items: accounts.map((account) {
        // Find account balance
        final accountBalance = _accountBalances
            .where((ab) => ab.account.id == account.id)
            .firstOrNull;
        final balance = accountBalance?.currentBalance ?? 0.0;

        return DropdownMenuItem<Account>(
          value: account,
          child: Row(
            children: [
              Text(account.type.icon),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      account.name,
                      overflow: TextOverflow.ellipsis,
                      style: AppTheme.bodyMedium.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      balance.toCurrencyString(),
                      style: AppTheme.bodySmall.copyWith(
                        color: balance.getAmountColor(),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      }).toList(),
      onChanged: (account) {
        setState(() {
          _selectedAccount = account;
        });
        // Check insufficient funds when account changes
        _checkInsufficientFunds();
      },
      validator: (value) {
        if (value == null) {
          return 'Please select an account';
        }
        return null;
      },
    );
  }

  Widget _buildLoadingDropdown() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          const Icon(Icons.account_balance_wallet, color: Colors.grey),
          const SizedBox(width: 12),
          Container(
            width: 120,
            height: 16,
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorDropdown() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: AppTheme.errorColor),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        'Error loading accounts. Please try again.',
        style: AppTheme.bodyMedium.copyWith(
          color: AppTheme.errorColor,
        ),
      ),
    );
  }
}
