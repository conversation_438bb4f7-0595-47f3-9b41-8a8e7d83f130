import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../core/theme/app_theme.dart';
import '../../core/utils/extensions.dart';
import '../../data/models/account.dart';
import '../../data/models/account_balance.dart';
import '../../providers/account_providers.dart';

class AccountsOverviewSection extends ConsumerStatefulWidget {
  const AccountsOverviewSection({super.key});

  @override
  ConsumerState<AccountsOverviewSection> createState() => _AccountsOverviewSectionState();
}

class _AccountsOverviewSectionState extends ConsumerState<AccountsOverviewSection> {
  final Map<String, DateTime> _accountUsageMap = {};

  @override
  void initState() {
    super.initState();
    _loadAccountUsage();
  }

  /// Load account usage data for smart ordering
  Future<void> _loadAccountUsage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => key.startsWith('account_usage_'));

      for (final key in keys) {
        final accountId = key.replaceFirst('account_usage_', '');
        final timestamp = prefs.getString(key);
        if (timestamp != null) {
          _accountUsageMap[accountId] = DateTime.parse(timestamp);
        }
      }

      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      debugPrint('Error loading account usage: $e');
    }
  }

  /// Update account usage when account is tapped
  Future<void> _updateAccountUsage(String accountId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final now = DateTime.now();
      await prefs.setString('account_usage_$accountId', now.toIso8601String());

      _accountUsageMap[accountId] = now;
      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      debugPrint('Error updating account usage: $e');
    }
  }

  /// Sort accounts by smart ordering (most recently used first, then by balance)
  List<AccountBalance> _sortAccountsSmart(List<AccountBalance> accounts) {
    final sortedAccounts = List<AccountBalance>.from(accounts);

    sortedAccounts.sort((a, b) {
      // First, sort by recent usage
      final aUsage = _accountUsageMap[a.account.id];
      final bUsage = _accountUsageMap[b.account.id];

      if (aUsage != null && bUsage != null) {
        final usageComparison = bUsage.compareTo(aUsage); // Most recent first
        if (usageComparison != 0) return usageComparison;
      } else if (aUsage != null) {
        return -1; // a has usage, b doesn't - a comes first
      } else if (bUsage != null) {
        return 1; // b has usage, a doesn't - b comes first
      }

      // If usage is equal or both null, sort by balance (highest first)
      return b.currentBalance.compareTo(a.currentBalance);
    });

    return sortedAccounts;
  }

  @override
  Widget build(BuildContext context) {
    final accountBalancesAsync = ref.watch(accountBalanceNotifierProvider);

    return accountBalancesAsync.when(
      data: (accountBalances) {
        final sortedAccounts = _sortAccountsSmart(accountBalances);
        return _buildAccountsList(sortedAccounts);
      },
      loading: () => _buildLoadingList(),
      error: (error, _) => _buildErrorWidget(),
    );
  }

  Widget _buildAccountsList(List<AccountBalance> accountBalances) {
    if (accountBalances.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      scrollDirection: Axis.horizontal,
      itemCount: accountBalances.length,
      itemBuilder: (context, index) {
        final accountBalance = accountBalances[index];
        return Padding(
          padding: EdgeInsets.only(
            right: index < accountBalances.length - 1 ? 12 : 0,
          ),
          child: _buildAccountCard(accountBalance),
        );
      },
    );
  }

  Widget _buildAccountCard(AccountBalance accountBalance) {
    final account = accountBalance.account;
    final balance = accountBalance.currentBalance;
    final isPositive = balance >= 0;

    return GestureDetector(
      onTap: () {
        // Update account usage for smart ordering
        _updateAccountUsage(account.id);
        // TODO: Navigate to account details
        debugPrint('Tapped account: ${account.name}');
      },
      child: Container(
        width: 180,
        padding: const EdgeInsets.all(20),
        decoration: AppTheme.cardDecoration.copyWith(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: isPositive
                        ? AppTheme.incomeColor.withValues(alpha: 0.1)
                        : AppTheme.expenseColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    account.type.icon,
                    style: TextStyle(
                      fontSize: 14,
                      color: isPositive ? AppTheme.incomeColor : AppTheme.expenseColor,
                    ),
                  ),
                ),
                const Spacer(),
                Container(
                  width: 6,
                  height: 6,
                  decoration: BoxDecoration(
                    color: account.isSynced
                        ? AppTheme.successColor
                        : AppTheme.warningColor,
                    shape: BoxShape.circle,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              account.name,
              style: AppTheme.labelLarge.copyWith(
                fontWeight: FontWeight.w600,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 2),
            Text(
              account.type.displayName,
              style: AppTheme.caption.copyWith(
                color: AppTheme.textTertiary,
              ),
            ),
            const SizedBox(height: 6),
            Text(
              balance.toCurrencyString(),
              style: AppTheme.amountTiny.copyWith(
                color: isPositive ? AppTheme.incomeColor : AppTheme.expenseColor,
                fontWeight: FontWeight.w700,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingList() {
    return ListView.builder(
      scrollDirection: Axis.horizontal,
      itemCount: 3,
      itemBuilder: (context, index) {
        return Padding(
          padding: EdgeInsets.only(right: index < 2 ? 12 : 0),
          child: _buildLoadingCard(),
        );
      },
    );
  }

  Widget _buildLoadingCard() {
    return Container(
      width: 160,
      padding: const EdgeInsets.all(6), // Minimal padding
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.grey[300]!,
          width: 1,
        ),
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
          Row(
            children: [
              Container(
                width: 24, // Match account card
                height: 24,
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
              const Spacer(),
              Container(
                width: 4, // Match account card
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  shape: BoxShape.circle,
                ),
              ),
            ],
          ),
          const SizedBox(height: 1), // Minimal spacing
          Flexible(
            child: Container(
              width: 100,
              height: 9, // Very small height
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
          Flexible(
            child: Container(
              width: 60,
              height: 7, // Very small height
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
          const SizedBox(height: 1),
          Flexible(
            child: Container(
              width: 80,
              height: 9, // Very small height
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
        ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.account_balance_wallet_outlined,
            size: 48,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 8),
          Text(
            'No accounts yet',
            style: AppTheme.bodyMedium.copyWith(
              color: AppTheme.textSecondary,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Add your first account to get started',
            style: AppTheme.bodySmall.copyWith(
              color: AppTheme.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 48,
            color: AppTheme.errorColor,
          ),
          const SizedBox(height: 8),
          Text(
            'Error loading accounts',
            style: AppTheme.bodyMedium.copyWith(
              color: AppTheme.errorColor,
            ),
          ),
        ],
      ),
    );
  }
}
