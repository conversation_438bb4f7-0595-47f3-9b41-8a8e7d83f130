import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/theme/app_theme.dart';
import '../../providers/service_providers.dart';
import '../../services/connectivity_service.dart';
import '../../services/sync_service.dart';

/// Widget that displays the current sync status and connection information
class SyncStatusIndicator extends ConsumerWidget {
  const SyncStatusIndicator({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final connectionInfo = ref.watch(connectionInfoProvider);
    final syncIndicator = ref.watch(syncIndicatorProvider);

    final isConnected = connectionInfo['isConnected'] as bool;
    final connectionType = connectionInfo['connectionType'] as ConnectionType;
    final statusString = connectionInfo['statusString'] as String;

    final syncStatus = syncIndicator['status'] as SyncStatus;
    final pendingCount = syncIndicator['pendingCount'] as int;
    final canSync = syncIndicator['canSync'] as bool;
    final lastSyncTime = syncIndicator['lastSyncTime'] as DateTime?;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: _getBackgroundColor(isConnected, syncStatus),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: _getBorderColor(isConnected, syncStatus),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Status Icon
          Icon(
            _getStatusIcon(isConnected, syncStatus, connectionType),
            size: 16,
            color: _getIconColor(isConnected, syncStatus),
          ),

          const SizedBox(width: 8),

          // Status Text
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  _getStatusText(isConnected, syncStatus, statusString),
                  style: AppTheme.bodySmall.copyWith(
                    fontWeight: FontWeight.w500,
                    color: _getTextColor(isConnected, syncStatus),
                  ),
                ),
                if (pendingCount > 0 || lastSyncTime != null)
                  Text(
                    _getSubtitleText(pendingCount, lastSyncTime),
                    style: AppTheme.bodySmall.copyWith(
                      fontSize: 11,
                      color: _getTextColor(isConnected, syncStatus).withValues(alpha: 0.7),
                    ),
                  ),
              ],
            ),
          ),

          // Sync Button (if applicable)
          if (canSync && pendingCount > 0)
            GestureDetector(
              onTap: () {
                ref.read(manualSyncProvider(null));
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  'Sync',
                  style: AppTheme.bodySmall.copyWith(
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.w600,
                    fontSize: 11,
                  ),
                ),
              ),
            ),

          // Loading indicator for syncing
          if (syncStatus == SyncStatus.syncing)
            const SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
              ),
            ),
        ],
      ),
    );
  }

  /// Get background color based on status
  Color _getBackgroundColor(bool isConnected, SyncStatus syncStatus) {
    if (!isConnected) {
      return AppTheme.warningColor.withValues(alpha: 0.1);
    }

    switch (syncStatus) {
      case SyncStatus.syncing:
        return AppTheme.primaryColor.withValues(alpha: 0.1);
      case SyncStatus.success:
        return AppTheme.successColor.withValues(alpha: 0.1);
      case SyncStatus.error:
        return AppTheme.errorColor.withValues(alpha: 0.1);
      case SyncStatus.idle:
        return Colors.grey.withValues(alpha: 0.1);
    }
  }

  /// Get border color based on status
  Color _getBorderColor(bool isConnected, SyncStatus syncStatus) {
    if (!isConnected) {
      return AppTheme.warningColor.withValues(alpha: 0.3);
    }

    switch (syncStatus) {
      case SyncStatus.syncing:
        return AppTheme.primaryColor.withValues(alpha: 0.3);
      case SyncStatus.success:
        return AppTheme.successColor.withValues(alpha: 0.3);
      case SyncStatus.error:
        return AppTheme.errorColor.withValues(alpha: 0.3);
      case SyncStatus.idle:
        return Colors.grey.withValues(alpha: 0.3);
    }
  }

  /// Get icon based on status
  IconData _getStatusIcon(bool isConnected, SyncStatus syncStatus, ConnectionType connectionType) {
    if (!isConnected) {
      return Icons.cloud_off;
    }

    switch (syncStatus) {
      case SyncStatus.syncing:
        return Icons.sync;
      case SyncStatus.success:
        return Icons.cloud_done;
      case SyncStatus.error:
        return Icons.sync_problem;
      case SyncStatus.idle:
        switch (connectionType) {
          case ConnectionType.wifi:
            return Icons.wifi;
          case ConnectionType.mobile:
            return Icons.signal_cellular_4_bar;
          default:
            return Icons.cloud;
        }
    }
  }

  /// Get icon color based on status
  Color _getIconColor(bool isConnected, SyncStatus syncStatus) {
    if (!isConnected) {
      return AppTheme.warningColor;
    }

    switch (syncStatus) {
      case SyncStatus.syncing:
        return AppTheme.primaryColor;
      case SyncStatus.success:
        return AppTheme.successColor;
      case SyncStatus.error:
        return AppTheme.errorColor;
      case SyncStatus.idle:
        return Colors.grey[600]!;
    }
  }

  /// Get text color based on status
  Color _getTextColor(bool isConnected, SyncStatus syncStatus) {
    if (!isConnected) {
      return AppTheme.warningColor;
    }

    switch (syncStatus) {
      case SyncStatus.syncing:
        return AppTheme.primaryColor;
      case SyncStatus.success:
        return AppTheme.successColor;
      case SyncStatus.error:
        return AppTheme.errorColor;
      case SyncStatus.idle:
        return AppTheme.textPrimary;
    }
  }

  /// Get status text
  String _getStatusText(bool isConnected, SyncStatus syncStatus, String connectionString) {
    if (!isConnected) {
      return 'Offline - Data saved locally';
    }

    switch (syncStatus) {
      case SyncStatus.syncing:
        return 'Syncing data...';
      case SyncStatus.success:
        return 'All data synced';
      case SyncStatus.error:
        return 'Sync failed - Will retry';
      case SyncStatus.idle:
        return 'Connected via $connectionString';
    }
  }

  /// Get subtitle text
  String _getSubtitleText(int pendingCount, DateTime? lastSyncTime) {
    final parts = <String>[];

    if (pendingCount > 0) {
      parts.add('$pendingCount pending');
    }

    if (lastSyncTime != null) {
      final now = DateTime.now();
      final difference = now.difference(lastSyncTime);

      if (difference.inMinutes < 1) {
        parts.add('Synced just now');
      } else if (difference.inHours < 1) {
        parts.add('Synced ${difference.inMinutes}m ago');
      } else if (difference.inDays < 1) {
        parts.add('Synced ${difference.inHours}h ago');
      } else {
        parts.add('Synced ${difference.inDays}d ago');
      }
    }

    return parts.join(' • ');
  }
}
