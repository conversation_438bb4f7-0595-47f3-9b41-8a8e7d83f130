import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/error/error_handler.dart';

/// A widget that displays user-friendly error messages
class ErrorDisplayWidget extends StatelessWidget {
  final dynamic error;
  final String? context;
  final VoidCallback? onRetry;
  final bool showDetails;

  const ErrorDisplayWidget({
    super.key,
    required this.error,
    this.context,
    this.onRetry,
    this.showDetails = false,
  });

  @override
  Widget build(BuildContext context) {
    final userMessage = ErrorHandler.handleError(error, context: this.context);

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Theme.of(context).colorScheme.error,
          ),
          const SizedBox(height: 16),
          Text(
            'Oops! Something went wrong',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Theme.of(context).colorScheme.error,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            userMessage,
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          if (showDetails && kDebugMode) ...[
            const SizedBox(height: 16),
            ExpansionTile(
              title: const Text('Technical Details'),
              children: [
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    error.toString(),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontFamily: 'monospace',
                    ),
                  ),
                ),
              ],
            ),
          ],
          if (onRetry != null) ...[
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: onRetry,
              icon: const Icon(Icons.refresh),
              label: const Text('Try Again'),
            ),
          ],
        ],
      ),
    );
  }
}

/// A compact error widget for inline display
class CompactErrorWidget extends StatelessWidget {
  final dynamic error;
  final String? context;
  final VoidCallback? onRetry;

  const CompactErrorWidget({
    super.key,
    required this.error,
    this.context,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    final userMessage = ErrorHandler.handleError(error, context: this.context);

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.errorContainer,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).colorScheme.error.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.warning_amber_rounded,
            color: Theme.of(context).colorScheme.error,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              userMessage,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onErrorContainer,
              ),
            ),
          ),
          if (onRetry != null) ...[
            const SizedBox(width: 8),
            IconButton(
              onPressed: onRetry,
              icon: const Icon(Icons.refresh),
              iconSize: 16,
              constraints: const BoxConstraints(
                minWidth: 32,
                minHeight: 32,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// A snackbar for showing error messages
class ErrorSnackBar {
  static void show(
    BuildContext context,
    dynamic error, {
    String? contextMessage,
    VoidCallback? onRetry,
  }) {
    final userMessage = ErrorHandler.handleError(error, context: contextMessage);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(userMessage),
            ),
          ],
        ),
        backgroundColor: Theme.of(context).colorScheme.error,
        action: onRetry != null
            ? SnackBarAction(
                label: 'Retry',
                textColor: Colors.white,
                onPressed: onRetry,
              )
            : null,
        duration: const Duration(seconds: 4),
      ),
    );
  }
}

/// Extension for easy error handling in AsyncValue
extension AsyncValueErrorHandling<T> on AsyncValue<T> {
  Widget when({
    required Widget Function(T data) data,
    required Widget Function(Object error, StackTrace stackTrace) error,
    required Widget Function() loading,
    String? errorContext,
    VoidCallback? onRetry,
  }) {
    return when(
      data: data,
      error: (err, stack) {
        // Log the error for debugging
        debugPrint('AsyncValue error in $errorContext: $err');
        debugPrint('Stack trace: $stack');

        return ErrorDisplayWidget(
          error: err,
          context: errorContext,
          onRetry: onRetry,
          showDetails: kDebugMode,
        );
      },
      loading: loading,
    );
  }

  Widget whenOrNull({
    Widget Function(T data)? data,
    Widget Function(Object error, StackTrace stackTrace)? error,
    Widget Function()? loading,
    String? errorContext,
    VoidCallback? onRetry,
  }) {
    return when(
      data: data ?? (value) => const SizedBox.shrink(),
      error: error ?? (err, stack) {
        // Log the error for debugging
        debugPrint('AsyncValue error in $errorContext: $err');
        debugPrint('Stack trace: $stack');

        return CompactErrorWidget(
          error: err,
          context: errorContext,
          onRetry: onRetry,
        );
      },
      loading: loading ?? () => const CircularProgressIndicator(),
    );
  }
}

/// Mixin for widgets that need error handling
mixin ErrorHandlingMixin {
  void handleError(
    BuildContext context,
    dynamic error, {
    String? contextMessage,
    bool showSnackBar = true,
    VoidCallback? onRetry,
  }) {
    // Always log the error for debugging
    debugPrint('Error in ${contextMessage ?? 'Unknown context'}: $error');

    if (showSnackBar) {
      ErrorSnackBar.show(
        context,
        error,
        contextMessage: contextMessage,
        onRetry: onRetry,
      );
    }
  }

  Future<T?> safeAsyncCall<T>(
    Future<T> Function() operation, {
    String? context,
    T? fallback,
  }) async {
    try {
      return await operation();
    } catch (error) {
      debugPrint('Safe async call failed in $context: $error');
      return fallback;
    }
  }
}
