import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../core/theme/app_theme.dart';
import '../../core/utils/extensions.dart';
import '../../providers/account_providers.dart';

class TotalBalanceCard extends ConsumerStatefulWidget {
  const TotalBalanceCard({super.key});

  @override
  ConsumerState<TotalBalanceCard> createState() => _TotalBalanceCardState();
}

class _TotalBalanceCardState extends ConsumerState<TotalBalanceCard> {
  double? _yesterdayBalance;
  bool _isLoadingTrend = true;

  @override
  void initState() {
    super.initState();
    _loadYesterdayBalance();
  }

  /// Load yesterday's balance for trend calculation
  Future<void> _loadYesterdayBalance() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final yesterday = DateTime.now().subtract(const Duration(days: 1));
      final yesterdayKey = 'balance_${yesterday.year}_${yesterday.month}_${yesterday.day}';

      _yesterdayBalance = prefs.getDouble(yesterdayKey);

      if (mounted) {
        setState(() {
          _isLoadingTrend = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading yesterday balance: $e');
      if (mounted) {
        setState(() {
          _isLoadingTrend = false;
        });
      }
    }
  }

  /// Save today's balance for future trend calculation
  Future<void> _saveTodayBalance(double balance) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final today = DateTime.now();
      final todayKey = 'balance_${today.year}_${today.month}_${today.day}';
      await prefs.setDouble(todayKey, balance);
    } catch (e) {
      debugPrint('Error saving today balance: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final totalBalanceAsync = ref.watch(totalBalanceNotifierProvider);

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(28),
      decoration: AppTheme.primaryCardDecoration,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'TOTAL BALANCE',
                    style: AppTheme.overline.copyWith(
                      color: Colors.white.withValues(alpha: 0.8),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Your Financial Overview',
                    style: AppTheme.caption.copyWith(
                      color: Colors.white.withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.account_balance_wallet_outlined,
                  color: Colors.white.withValues(alpha: 0.9),
                  size: 24,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          totalBalanceAsync.when(
            data: (balance) {
              // Save today's balance for future trend calculation
              _saveTodayBalance(balance);
              return _buildBalanceDisplay(balance);
            },
            loading: () => _buildLoadingDisplay(),
            error: (error, _) => _buildErrorDisplay(),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: AppTheme.successColor,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                'Live • Updated just now',
                style: AppTheme.caption.copyWith(
                  color: Colors.white.withValues(alpha: 0.8),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBalanceDisplay(double balance) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                balance.toCurrencyString(),
                style: AppTheme.amountLarge.copyWith(
                  color: Colors.white,
                  fontSize: 32,
                ),
              ),
            ),
            if (!_isLoadingTrend && _yesterdayBalance != null)
              _buildTrendIndicator(balance, _yesterdayBalance!),
          ],
        ),
        if (!_isLoadingTrend && _yesterdayBalance != null)
          _buildTrendText(balance, _yesterdayBalance!),
      ],
    );
  }

  /// Build trend indicator (arrow and percentage)
  Widget _buildTrendIndicator(double currentBalance, double yesterdayBalance) {
    final difference = currentBalance - yesterdayBalance;
    final percentageChange = yesterdayBalance != 0
        ? (difference / yesterdayBalance.abs()) * 100
        : 0.0;

    if (difference == 0) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.remove,
              color: Colors.white.withValues(alpha: 0.9),
              size: 16,
            ),
            const SizedBox(width: 4),
            Text(
              '0%',
              style: AppTheme.bodySmall.copyWith(
                color: Colors.white.withValues(alpha: 0.9),
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      );
    }

    final isPositive = difference > 0;
    final color = isPositive
        ? AppTheme.successColor
        : AppTheme.errorColor;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isPositive ? Icons.trending_up : Icons.trending_down,
            color: color,
            size: 16,
          ),
          const SizedBox(width: 4),
          Text(
            '${percentageChange.abs().toStringAsFixed(1)}%',
            style: AppTheme.bodySmall.copyWith(
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// Build trend text description
  Widget _buildTrendText(double currentBalance, double yesterdayBalance) {
    final difference = currentBalance - yesterdayBalance;

    if (difference == 0) {
      return Text(
        'No change from yesterday',
        style: AppTheme.bodySmall.copyWith(
          color: Colors.white.withValues(alpha: 0.7),
        ),
      );
    }

    final isPositive = difference > 0;
    final changeText = isPositive ? 'increase' : 'decrease';

    return Text(
      '${difference.abs().toCurrencyString()} $changeText from yesterday',
      style: AppTheme.bodySmall.copyWith(
        color: Colors.white.withValues(alpha: 0.7),
      ),
    );
  }

  Widget _buildLoadingDisplay() {
    return Container(
      height: 40,
      width: 200,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Center(
        child: SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
          ),
        ),
      ),
    );
  }

  Widget _buildErrorDisplay() {
    return Text(
      'Error loading balance',
      style: AppTheme.titleMedium.copyWith(
        color: Colors.white.withValues(alpha: 0.9),
      ),
    );
  }
}
