{"indexes": [{"collectionGroup": "transactions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}]}, {"collectionGroup": "transactions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}]}, {"collectionGroup": "transactions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "categoryId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}]}, {"collectionGroup": "transactions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}]}, {"collectionGroup": "transactions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "categoryId", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}]}, {"collectionGroup": "transfers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "fromAccountId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}]}, {"collectionGroup": "transfers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "toAccountId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}]}, {"collectionGroup": "transfers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "fromAccountId", "order": "ASCENDING"}, {"fieldPath": "toAccountId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}]}, {"collectionGroup": "categories", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isDefault", "order": "ASCENDING"}, {"fieldPath": "name", "order": "ASCENDING"}]}, {"collectionGroup": "categories", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "name", "order": "ASCENDING"}]}], "fieldOverrides": [{"collectionGroup": "transactions", "fieldPath": "description", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}, {"arrayConfig": "CONTAINS", "queryScope": "COLLECTION"}]}, {"collectionGroup": "categories", "fieldPath": "keywords", "indexes": [{"arrayConfig": "CONTAINS", "queryScope": "COLLECTION"}]}, {"collectionGroup": "transfers", "fieldPath": "description", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}]}]}