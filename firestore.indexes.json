{"indexes": [{"collectionGroup": "accounts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "transactions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "date", "order": "DESCENDING"}]}, {"collectionGroup": "transactions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "accountId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}]}, {"collectionGroup": "transactions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "updatedAt", "order": "DESCENDING"}]}], "fieldOverrides": []}