rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions for validation
    function isAuthenticated() {
      return request.auth != null;
    }

    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }

    function isValidTimestamp(timestamp) {
      return timestamp is timestamp;
    }

    function isValidAmount(amount) {
      return amount is number && amount >= 0 && amount <= 999999.99;
    }

    function isValidString(str, minLength, maxLength) {
      return str is string && str.size() >= minLength && str.size() <= maxLength;
    }

    // User documents and subcollections
    match /users/{userId} {
      // Allow authenticated users to access their own user document
      allow read, write: if isOwner(userId);

      // User's accounts subcollection
      match /accounts/{accountId} {
        allow read, write: if isOwner(userId) && validateAccount();

        function validateAccount() {
          return request.resource.data.keys().hasAll(['id', 'name', 'type', 'createdAt', 'updatedAt']) &&
                 isValidString(request.resource.data.name, 1, 50) &&
                 request.resource.data.type in ['cash', 'bank'] &&
                 isValidTimestamp(request.resource.data.createdAt) &&
                 isValidTimestamp(request.resource.data.updatedAt);
        }
      }

      // User's transactions subcollection
      match /transactions/{transactionId} {
        allow read, write: if isOwner(userId) && validateTransaction();

        function validateTransaction() {
          return request.resource.data.keys().hasAll(['id', 'accountId', 'type', 'amount', 'description', 'date', 'createdAt', 'updatedAt']) &&
                 isValidString(request.resource.data.accountId, 1, 100) &&
                 request.resource.data.type in ['income', 'expense'] &&
                 isValidAmount(request.resource.data.amount) &&
                 isValidString(request.resource.data.description, 1, 200) &&
                 isValidTimestamp(request.resource.data.date) &&
                 isValidTimestamp(request.resource.data.createdAt) &&
                 isValidTimestamp(request.resource.data.updatedAt) &&
                 // Optional fields validation
                 (!request.resource.data.keys().hasAny(['categoryId']) || isValidString(request.resource.data.categoryId, 1, 100)) &&
                 (!request.resource.data.keys().hasAny(['note']) || isValidString(request.resource.data.note, 0, 500));
        }
      }

      // User's categories subcollection (Phase 2)
      match /categories/{categoryId} {
        allow read, write: if isOwner(userId) && validateCategory();

        function validateCategory() {
          return request.resource.data.keys().hasAll(['id', 'name', 'icon', 'color', 'createdAt', 'updatedAt']) &&
                 isValidString(request.resource.data.name, 1, 30) &&
                 isValidString(request.resource.data.icon, 1, 10) &&
                 isValidString(request.resource.data.color, 7, 7) && // Hex color format
                 request.resource.data.color.matches('^#[0-9A-Fa-f]{6}$') &&
                 isValidTimestamp(request.resource.data.createdAt) &&
                 isValidTimestamp(request.resource.data.updatedAt) &&
                 // Optional fields validation
                 (!request.resource.data.keys().hasAny(['monthlyBudget']) || isValidAmount(request.resource.data.monthlyBudget)) &&
                 (!request.resource.data.keys().hasAny(['keywords']) || request.resource.data.keywords is list);
        }
      }

      // User's transfers subcollection (Phase 2)
      match /transfers/{transferId} {
        allow read, write: if isOwner(userId) && validateTransfer();

        function validateTransfer() {
          return request.resource.data.keys().hasAll(['id', 'amount', 'fromAccountId', 'toAccountId', 'date', 'createdAt']) &&
                 isValidAmount(request.resource.data.amount) &&
                 isValidString(request.resource.data.fromAccountId, 1, 100) &&
                 isValidString(request.resource.data.toAccountId, 1, 100) &&
                 request.resource.data.fromAccountId != request.resource.data.toAccountId &&
                 isValidTimestamp(request.resource.data.date) &&
                 isValidTimestamp(request.resource.data.createdAt) &&
                 // Optional fields validation
                 (!request.resource.data.keys().hasAny(['description']) || isValidString(request.resource.data.description, 0, 200));
        }
      }

      // User's preferences subcollection (Phase 2)
      match /preferences/{preferenceId} {
        allow read, write: if isOwner(userId) && validatePreferences();

        function validatePreferences() {
          return request.resource.data.keys().hasAll(['id', 'updatedAt']) &&
                 isValidTimestamp(request.resource.data.updatedAt);
        }
      }
    }

    // Deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
