#!/bin/bash

# Test Firestore Security Rules Script
# This script runs comprehensive tests on Firestore security rules

set -e  # Exit on any error

echo "🧪 Starting Firestore Rules Testing..."

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "❌ Firebase CLI is not installed. Please install it first:"
    echo "   npm install -g firebase-tools"
    exit 1
fi

# Get the current directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "📁 Project root: $PROJECT_ROOT"

# Change to project root
cd "$PROJECT_ROOT"

# Check if firestore.rules exists
if [ ! -f "firestore.rules" ]; then
    echo "❌ firestore.rules not found."
    exit 1
fi

echo "✅ Firestore rules file found."

# Validate Firestore rules syntax
echo "🔍 Validating Firestore rules syntax..."
if firebase emulators:exec --only firestore "echo 'Rules validation passed'" 2>/dev/null; then
    echo "✅ Firestore rules syntax is valid."
else
    echo "⚠️  Cannot validate rules without emulator. Checking file syntax manually..."
    # Basic syntax check - ensure file is not empty and has basic structure
    if grep -q "rules_version" firestore.rules && grep -q "service cloud.firestore" firestore.rules; then
        echo "✅ Basic Firestore rules syntax appears valid."
    else
        echo "❌ Firestore rules file appears to have syntax issues."
        exit 1
    fi
fi

# Create test data directory if it doesn't exist
mkdir -p test/firestore

# Create test cases file
cat > test/firestore/rules_test.js << 'EOF'
const { assertFails, assertSucceeds, initializeTestEnvironment } = require('@firebase/rules-unit-testing');
const fs = require('fs');

let testEnv;

beforeAll(async () => {
  testEnv = await initializeTestEnvironment({
    projectId: 'expense-tracker-test',
    firestore: {
      rules: fs.readFileSync('firestore.rules', 'utf8'),
    },
  });
});

afterAll(async () => {
  await testEnv.cleanup();
});

describe('Firestore Security Rules', () => {
  const userId = 'test-user-123';
  const otherUserId = 'other-user-456';

  describe('Authentication Tests', () => {
    test('should deny access to unauthenticated users', async () => {
      const unauthedDb = testEnv.unauthenticatedContext().firestore();
      await assertFails(unauthedDb.collection('users').doc(userId).get());
    });

    test('should allow access to authenticated users for their own data', async () => {
      const authedDb = testEnv.authenticatedContext(userId).firestore();
      await assertSucceeds(authedDb.collection('users').doc(userId).get());
    });

    test('should deny access to other users data', async () => {
      const authedDb = testEnv.authenticatedContext(userId).firestore();
      await assertFails(authedDb.collection('users').doc(otherUserId).get());
    });
  });

  describe('Accounts Collection Tests', () => {
    test('should allow valid account creation', async () => {
      const authedDb = testEnv.authenticatedContext(userId).firestore();
      const validAccount = {
        id: 'account-123',
        name: 'Test Account',
        type: 'bank',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      await assertSucceeds(
        authedDb.collection('users').doc(userId)
          .collection('accounts').doc('account-123')
          .set(validAccount)
      );
    });

    test('should reject invalid account type', async () => {
      const authedDb = testEnv.authenticatedContext(userId).firestore();
      const invalidAccount = {
        id: 'account-123',
        name: 'Test Account',
        type: 'invalid-type',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      await assertFails(
        authedDb.collection('users').doc(userId)
          .collection('accounts').doc('account-123')
          .set(invalidAccount)
      );
    });
  });

  describe('Transactions Collection Tests', () => {
    test('should allow valid transaction creation', async () => {
      const authedDb = testEnv.authenticatedContext(userId).firestore();
      const validTransaction = {
        id: 'transaction-123',
        accountId: 'account-123',
        type: 'expense',
        amount: 100.50,
        description: 'Test transaction',
        date: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      await assertSucceeds(
        authedDb.collection('users').doc(userId)
          .collection('transactions').doc('transaction-123')
          .set(validTransaction)
      );
    });

    test('should reject invalid transaction type', async () => {
      const authedDb = testEnv.authenticatedContext(userId).firestore();
      const invalidTransaction = {
        id: 'transaction-123',
        accountId: 'account-123',
        type: 'invalid-type',
        amount: 100.50,
        description: 'Test transaction',
        date: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      await assertFails(
        authedDb.collection('users').doc(userId)
          .collection('transactions').doc('transaction-123')
          .set(invalidTransaction)
      );
    });

    test('should reject negative amounts', async () => {
      const authedDb = testEnv.authenticatedContext(userId).firestore();
      const invalidTransaction = {
        id: 'transaction-123',
        accountId: 'account-123',
        type: 'expense',
        amount: -100.50,
        description: 'Test transaction',
        date: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      await assertFails(
        authedDb.collection('users').doc(userId)
          .collection('transactions').doc('transaction-123')
          .set(invalidTransaction)
      );
    });
  });

  describe('Categories Collection Tests', () => {
    test('should allow valid category creation', async () => {
      const authedDb = testEnv.authenticatedContext(userId).firestore();
      const validCategory = {
        id: 'category-123',
        name: 'Test Category',
        icon: '🍕',
        color: '#FF5722',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      await assertSucceeds(
        authedDb.collection('users').doc(userId)
          .collection('categories').doc('category-123')
          .set(validCategory)
      );
    });

    test('should reject invalid color format', async () => {
      const authedDb = testEnv.authenticatedContext(userId).firestore();
      const invalidCategory = {
        id: 'category-123',
        name: 'Test Category',
        icon: '🍕',
        color: 'invalid-color',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      await assertFails(
        authedDb.collection('users').doc(userId)
          .collection('categories').doc('category-123')
          .set(invalidCategory)
      );
    });
  });

  describe('Transfers Collection Tests', () => {
    test('should allow valid transfer creation', async () => {
      const authedDb = testEnv.authenticatedContext(userId).firestore();
      const validTransfer = {
        id: 'transfer-123',
        amount: 100.00,
        fromAccountId: 'account-1',
        toAccountId: 'account-2',
        date: new Date(),
        createdAt: new Date(),
      };

      await assertSucceeds(
        authedDb.collection('users').doc(userId)
          .collection('transfers').doc('transfer-123')
          .set(validTransfer)
      );
    });

    test('should reject transfer to same account', async () => {
      const authedDb = testEnv.authenticatedContext(userId).firestore();
      const invalidTransfer = {
        id: 'transfer-123',
        amount: 100.00,
        fromAccountId: 'account-1',
        toAccountId: 'account-1',
        date: new Date(),
        createdAt: new Date(),
      };

      await assertFails(
        authedDb.collection('users').doc(userId)
          .collection('transfers').doc('transfer-123')
          .set(invalidTransfer)
      );
    });
  });
});
EOF

echo "📝 Test cases created."

# Check if Node.js is available for running tests
if command -v node &> /dev/null; then
    echo "🔍 Node.js found. You can run the tests with:"
    echo "   cd test/firestore && npm install @firebase/rules-unit-testing && npm test"
else
    echo "⚠️  Node.js not found. Install Node.js to run automated tests."
fi

echo ""
echo "✅ Firestore rules validation completed successfully!"
echo ""
echo "📋 Validation Summary:"
echo "   ✅ Syntax validation passed"
echo "   ✅ Test cases generated"
echo "   ✅ Ready for deployment"
echo ""
echo "🧪 To run comprehensive tests:"
echo "   1. Install Node.js if not already installed"
echo "   2. cd test/firestore"
echo "   3. npm install @firebase/rules-unit-testing jest"
echo "   4. npx jest rules_test.js"
echo ""
echo "🚀 To deploy the rules:"
echo "   ./scripts/deploy_firestore.sh"
