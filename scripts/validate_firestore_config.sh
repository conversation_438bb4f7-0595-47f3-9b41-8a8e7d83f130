#!/bin/bash

# Validate Firestore Configuration Script
# This script validates Firestore rules and indexes without requiring Firebase CLI

set -e  # Exit on any error

echo "🔍 Validating Firestore Configuration..."

# Get the current directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "📁 Project root: $PROJECT_ROOT"

# Change to project root
cd "$PROJECT_ROOT"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "success") echo -e "${GREEN}✅ $message${NC}" ;;
        "error") echo -e "${RED}❌ $message${NC}" ;;
        "warning") echo -e "${YELLOW}⚠️  $message${NC}" ;;
        "info") echo -e "${BLUE}ℹ️  $message${NC}" ;;
    esac
}

# Validation counters
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# Function to run a check
run_check() {
    local check_name=$1
    local check_command=$2
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    echo ""
    echo "🔍 Checking: $check_name"
    
    if eval "$check_command"; then
        print_status "success" "$check_name passed"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        print_status "error" "$check_name failed"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        return 1
    fi
}

echo ""
echo "🚀 Starting Firestore configuration validation..."

# Check 1: Firestore rules file exists
run_check "Firestore rules file exists" "[ -f 'firestore.rules' ]"

# Check 2: Firestore indexes file exists
run_check "Firestore indexes file exists" "[ -f 'firestore.indexes.json' ]"

# Check 3: Firebase config file exists
run_check "Firebase config file exists" "[ -f 'firebase.json' ]"

# Check 4: Firestore rules has correct version
run_check "Firestore rules version" "grep -q \"rules_version = '2'\" firestore.rules"

# Check 5: Firestore rules has service declaration
run_check "Firestore service declaration" "grep -q 'service cloud.firestore' firestore.rules"

# Check 6: Firestore rules has user isolation
run_check "User isolation in rules" "grep -q 'request.auth.uid == userId' firestore.rules"

# Check 7: Firestore rules has validation functions
run_check "Validation functions in rules" "grep -q 'function isValidAmount' firestore.rules"

# Check 8: Firestore indexes is valid JSON
run_check "Firestore indexes JSON validity" "python3 -m json.tool firestore.indexes.json > /dev/null 2>&1 || node -e 'JSON.parse(require(\"fs\").readFileSync(\"firestore.indexes.json\"))' > /dev/null 2>&1"

# Check 9: Firestore indexes has required collections
run_check "Transaction indexes present" "grep -q '\"collectionGroup\": \"transactions\"' firestore.indexes.json"

# Check 10: Phase 2 collections in indexes
run_check "Categories indexes present" "grep -q '\"collectionGroup\": \"categories\"' firestore.indexes.json"

# Check 11: Transfer indexes present
run_check "Transfers indexes present" "grep -q '\"collectionGroup\": \"transfers\"' firestore.indexes.json"

# Check 12: Field overrides present
run_check "Field overrides present" "grep -q '\"fieldOverrides\"' firestore.indexes.json"

# Check 13: Categories collection rules
run_check "Categories collection rules" "grep -q 'match /categories/' firestore.rules"

# Check 14: Transfers collection rules
run_check "Transfers collection rules" "grep -q 'match /transfers/' firestore.rules"

# Check 15: Preferences collection rules
run_check "Preferences collection rules" "grep -q 'match /preferences/' firestore.rules"

# Check 16: Color validation in rules
run_check "Color validation in rules" "grep -q 'matches.*#.*[0-9A-Fa-f]' firestore.rules"

# Check 17: Transfer account validation
run_check "Transfer account validation" "grep -q 'fromAccountId != .*toAccountId' firestore.rules"

# Check 18: Amount validation in rules
run_check "Amount validation in rules" "grep -q 'isValidAmount' firestore.rules"

echo ""
echo "📊 Validation Summary:"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "Total Checks: $TOTAL_CHECKS"
print_status "success" "Passed: $PASSED_CHECKS"
if [ $FAILED_CHECKS -gt 0 ]; then
    print_status "error" "Failed: $FAILED_CHECKS"
else
    print_status "success" "Failed: $FAILED_CHECKS"
fi

echo ""
if [ $FAILED_CHECKS -eq 0 ]; then
    print_status "success" "All validation checks passed! 🎉"
    echo ""
    echo "🚀 Your Firestore configuration is ready for deployment!"
    echo ""
    echo "Next steps:"
    echo "1. Deploy rules: firebase deploy --only firestore:rules"
    echo "2. Deploy indexes: firebase deploy --only firestore:indexes"
    echo "3. Or use the deployment script: ./scripts/deploy_firestore.sh"
    exit 0
else
    print_status "error" "Some validation checks failed!"
    echo ""
    echo "Please fix the issues above before deploying to production."
    exit 1
fi
