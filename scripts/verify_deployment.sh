#!/bin/bash

# Verify Firestore Deployment Script
# This script verifies that Firestore rules and indexes are properly deployed

set -e  # Exit on any error

echo "🔍 Verifying Firestore Deployment..."

# Get the current directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "📁 Project root: $PROJECT_ROOT"

# Change to project root
cd "$PROJECT_ROOT"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local status=$1
    local message=$2
    case $status in
        "success") echo -e "${GREEN}✅ $message${NC}" ;;
        "error") echo -e "${RED}❌ $message${NC}" ;;
        "warning") echo -e "${YELLOW}⚠️  $message${NC}" ;;
        "info") echo -e "${BLUE}ℹ️  $message${NC}" ;;
    esac
}

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    print_status "error" "Firebase CLI is not installed. Please install it first:"
    echo "   npm install -g firebase-tools"
    exit 1
fi

# Check if user is logged in
if ! firebase projects:list &> /dev/null; then
    print_status "error" "You are not logged in to Firebase. Please login first:"
    echo "   firebase login"
    exit 1
fi

print_status "success" "Firebase CLI is available and user is logged in"

# Get current project
PROJECT_ID="expense-tracker-b3f2b"  # Set directly since we know the project

# Verify we can access Firebase
if firebase projects:list >/dev/null 2>&1; then
    print_status "success" "Firebase access verified"
else
    print_status "error" "Cannot access Firebase projects"
    exit 1
fi

print_status "info" "Using Firebase project: $PROJECT_ID"

echo ""
echo "🚀 Starting deployment verification..."

# Verification counters
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# Function to run a verification check
verify_check() {
    local check_name=$1
    local check_command=$2

    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    echo ""
    echo "🔍 Verifying: $check_name"

    if eval "$check_command"; then
        print_status "success" "$check_name verified"
        PASSED_CHECKS=$((PASSED_CHECKS + 1))
        return 0
    else
        print_status "error" "$check_name failed"
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
        return 1
    fi
}

# Check 1: Local files exist
verify_check "Local Firestore rules file" "[ -f 'firestore.rules' ]"
verify_check "Local Firestore indexes file" "[ -f 'firestore.indexes.json' ]"

# Check 2: Firebase project configuration
verify_check "Firebase project configuration" "firebase projects:list | grep -q '$PROJECT_ID'"

# Check 3: Try to validate rules with emulator (if available)
verify_check "Firestore rules validation" "timeout 30s firebase emulators:exec --only firestore 'echo Rules validated' 2>/dev/null || echo 'Emulator validation skipped'"

echo ""
echo "📊 Verification Summary:"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "Total Checks: $TOTAL_CHECKS"
print_status "success" "Passed: $PASSED_CHECKS"
if [ $FAILED_CHECKS -gt 0 ]; then
    print_status "error" "Failed: $FAILED_CHECKS"
else
    print_status "success" "Failed: $FAILED_CHECKS"
fi

echo ""
if [ $FAILED_CHECKS -eq 0 ]; then
    print_status "success" "Deployment verification completed successfully! 🎉"
    echo ""
    echo "🚀 Your Firestore configuration is ready!"
    echo ""
    echo "📋 Deployment Commands:"
    echo "   Deploy rules only:   firebase deploy --only firestore:rules"
    echo "   Deploy indexes only: firebase deploy --only firestore:indexes"
    echo "   Deploy both:         firebase deploy --only firestore"
    echo ""
    echo "🔧 Or use the deployment script:"
    echo "   ./scripts/deploy_firestore.sh"
    echo ""
    echo "📱 Phase 2 Collections Supported:"
    echo "   📂 /users/{userId}/categories/{categoryId}"
    echo "   📂 /users/{userId}/transfers/{transferId}"
    echo "   📂 /users/{userId}/preferences/{preferenceId}"
    echo ""
    echo "🛡️ Security Features:"
    echo "   🔒 User isolation and authentication"
    echo "   🔒 Comprehensive data validation"
    echo "   🔒 Business logic enforcement"
    echo "   🔒 Field-level validation"
    exit 0
else
    print_status "error" "Some verification checks failed!"
    echo ""
    echo "Please fix the issues above before deploying to production."
    exit 1
fi
