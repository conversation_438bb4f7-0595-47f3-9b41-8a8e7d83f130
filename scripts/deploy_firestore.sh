#!/bin/bash

# Deploy Firestore Rules and Indexes Script
# This script deploys the updated Firestore security rules and database indexes

set -e  # Exit on any error

echo "🚀 Starting Firestore deployment..."

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "❌ Firebase CLI is not installed. Please install it first:"
    echo "   npm install -g firebase-tools"
    exit 1
fi

# Check if user is logged in
if ! firebase projects:list &> /dev/null; then
    echo "❌ You are not logged in to Firebase. Please login first:"
    echo "   firebase login"
    exit 1
fi

# Get the current directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

echo "📁 Project root: $PROJECT_ROOT"

# Change to project root
cd "$PROJECT_ROOT"

# Check if firebase.json exists
if [ ! -f "firebase.json" ]; then
    echo "❌ firebase.json not found. Please run 'firebase init' first."
    exit 1
fi

# Check if firestore.rules exists
if [ ! -f "firestore.rules" ]; then
    echo "❌ firestore.rules not found."
    exit 1
fi

# Check if firestore.indexes.json exists
if [ ! -f "firestore.indexes.json" ]; then
    echo "❌ firestore.indexes.json not found."
    exit 1
fi

echo "✅ All required files found."

# Validate Firestore rules
echo "🔍 Validating Firestore rules..."
# Basic syntax check - ensure file has proper structure
if grep -q "rules_version" firestore.rules && grep -q "service cloud.firestore" firestore.rules; then
    echo "✅ Firestore rules syntax appears valid."
else
    echo "❌ Firestore rules file appears to have syntax issues."
    exit 1
fi

# Deploy Firestore rules
echo "📤 Deploying Firestore rules..."
if firebase deploy --only firestore:rules; then
    echo "✅ Firestore rules deployed successfully."
else
    echo "❌ Failed to deploy Firestore rules."
    exit 1
fi

# Deploy Firestore indexes
echo "📤 Deploying Firestore indexes..."
if firebase deploy --only firestore:indexes; then
    echo "✅ Firestore indexes deployed successfully."
else
    echo "❌ Failed to deploy Firestore indexes."
    exit 1
fi

echo ""
echo "🎉 Firestore deployment completed successfully!"
echo ""
echo "📋 Summary of deployed components:"
echo "   ✅ Security Rules - Updated with Phase 2 validation"
echo "   ✅ Database Indexes - Optimized for Phase 2 queries"
echo ""
echo "🔧 New collections supported:"
echo "   📂 categories - Category management with validation"
echo "   📂 transfers - Money transfer tracking"
echo "   📂 preferences - User settings and preferences"
echo ""
echo "⚡ Performance optimizations:"
echo "   🔍 Transaction queries by category, type, and date"
echo "   🔍 Transfer queries by account and date range"
echo "   🔍 Category queries by status and name"
echo "   🔍 Full-text search support for descriptions"
echo ""
echo "🛡️ Security enhancements:"
echo "   🔒 Comprehensive data validation"
echo "   🔒 User isolation and authentication checks"
echo "   🔒 Field-level validation for all data types"
echo "   🔒 Protection against malicious data injection"
echo ""
echo "📱 Ready for Phase 2 features!"
