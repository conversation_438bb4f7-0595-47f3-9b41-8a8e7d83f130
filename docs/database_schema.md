# Database Schema Documentation

## Overview

This document outlines the complete database schema for the Expense Tracker application, including both local (Hive) and cloud (Firestore) storage structures.

## Firestore Collections Structure

```
/users/{userId}
├── /accounts/{accountId}
├── /transactions/{transactionId}
├── /categories/{categoryId}        [Phase 2]
├── /transfers/{transferId}         [Phase 2]
└── /preferences/{preferenceId}     [Phase 2]
```

## Collection Schemas

### 1. Accounts Collection

**Path**: `/users/{userId}/accounts/{accountId}`

```typescript
interface Account {
  id: string;                    // Unique account identifier
  name: string;                  // Account name (1-50 chars)
  type: 'cash' | 'bank';        // Account type
  initialBalance: number;        // Starting balance
  createdAt: Timestamp;          // Creation timestamp
  updatedAt: Timestamp;          // Last update timestamp
  isDeleted: boolean;            // Soft delete flag
}
```

**Validation Rules**:
- `name`: 1-50 characters, required
- `type`: Must be 'cash' or 'bank'
- `initialBalance`: Number, 0-999999.99
- Timestamps: Valid Firestore timestamps

### 2. Transactions Collection

**Path**: `/users/{userId}/transactions/{transactionId}`

```typescript
interface Transaction {
  id: string;                    // Unique transaction identifier
  accountId: string;             // Reference to account
  type: 'income' | 'expense';    // Transaction type
  amount: number;                // Transaction amount
  description: string;           // Transaction description (1-200 chars)
  date: Timestamp;               // Transaction date
  createdAt: Timestamp;          // Creation timestamp
  updatedAt: Timestamp;          // Last update timestamp
  categoryId?: string;           // Category reference [Phase 2]
  note?: string;                 // Optional note (0-500 chars)
  isDeleted: boolean;            // Soft delete flag
}
```

**Validation Rules**:
- `accountId`: 1-100 characters, required
- `type`: Must be 'income' or 'expense'
- `amount`: Number, 0-999999.99
- `description`: 1-200 characters, required
- `categoryId`: 1-100 characters, optional
- `note`: 0-500 characters, optional

### 3. Categories Collection [Phase 2]

**Path**: `/users/{userId}/categories/{categoryId}`

```typescript
interface Category {
  id: string;                    // Unique category identifier
  name: string;                  // Category name (1-30 chars)
  icon: string;                  // Emoji icon (1-10 chars)
  color: string;                 // Hex color (#RRGGBB)
  isDefault: boolean;            // Default category flag
  keywords: string[];            // Search keywords array
  monthlyBudget?: number;        // Optional monthly budget
  isActive: boolean;             // Active status
  createdAt: Timestamp;          // Creation timestamp
  updatedAt: Timestamp;          // Last update timestamp
  isDeleted: boolean;            // Soft delete flag
}
```

**Validation Rules**:
- `name`: 1-30 characters, required, unique per user
- `icon`: 1-10 characters, required
- `color`: Exactly 7 characters, hex format (#RRGGBB)
- `keywords`: Array of strings
- `monthlyBudget`: Number, 0-999999.99, optional

### 4. Transfers Collection [Phase 2]

**Path**: `/users/{userId}/transfers/{transferId}`

```typescript
interface Transfer {
  id: string;                    // Unique transfer identifier
  amount: number;                // Transfer amount
  fromAccountId: string;         // Source account reference
  toAccountId: string;           // Destination account reference
  description?: string;          // Optional description (0-200 chars)
  date: Timestamp;               // Transfer date
  createdAt: Timestamp;          // Creation timestamp
  isDeleted: boolean;            // Soft delete flag
}
```

**Validation Rules**:
- `amount`: Number, 0-999999.99, required
- `fromAccountId`: 1-100 characters, required
- `toAccountId`: 1-100 characters, required
- `fromAccountId` ≠ `toAccountId`: Must be different accounts
- `description`: 0-200 characters, optional

### 5. Preferences Collection [Phase 2]

**Path**: `/users/{userId}/preferences/{preferenceId}`

```typescript
interface UserPreferences {
  id: string;                    // Preference identifier
  currency: string;              // Primary currency code
  dateFormat: string;            // Date format preference
  theme: string;                 // App theme preference
  notifications: object;         // Notification settings
  updatedAt: Timestamp;          // Last update timestamp
}
```

## Database Indexes

### Composite Indexes

#### Transactions Collection
1. **Account + Date**: `(accountId ASC, date DESC)`
2. **Type + Date**: `(type ASC, date DESC)`
3. **Category + Date**: `(categoryId ASC, date DESC)`
4. **Account + Type + Date**: `(accountId ASC, type ASC, date DESC)`
5. **Category + Type + Date**: `(categoryId ASC, type ASC, date DESC)`

#### Transfers Collection
1. **From Account + Date**: `(fromAccountId ASC, date DESC)`
2. **To Account + Date**: `(toAccountId ASC, date DESC)`
3. **Account Pair + Date**: `(fromAccountId ASC, toAccountId ASC, date DESC)`

#### Categories Collection
1. **Default + Name**: `(isDefault ASC, name ASC)`
2. **Active + Name**: `(isActive ASC, name ASC)`

### Field Overrides

#### Search Indexes
1. **Transaction Descriptions**: Text search and array contains
2. **Category Keywords**: Array contains for keyword matching
3. **Transfer Descriptions**: Text search

## Security Rules

### Authentication
- All operations require user authentication
- Users can only access their own data
- User ID must match the authenticated user's UID

### Data Validation
- Comprehensive field validation for all collections
- Type checking for all data types
- Range validation for numeric fields
- String length validation
- Required field enforcement

### Business Logic Validation
- Transfer accounts must be different
- Amounts must be positive and within limits
- Color codes must be valid hex format
- Timestamps must be valid Firestore timestamps

## Query Patterns

### Common Queries

#### Transactions
```typescript
// Get transactions for account, ordered by date
transactions
  .where('accountId', '==', accountId)
  .orderBy('date', 'desc')

// Get transactions by category
transactions
  .where('categoryId', '==', categoryId)
  .orderBy('date', 'desc')

// Get transactions by type and date range
transactions
  .where('type', '==', 'expense')
  .where('date', '>=', startDate)
  .where('date', '<=', endDate)
  .orderBy('date', 'desc')
```

#### Transfers
```typescript
// Get transfers for account (as source)
transfers
  .where('fromAccountId', '==', accountId)
  .orderBy('date', 'desc')

// Get transfers between accounts
transfers
  .where('fromAccountId', '==', fromId)
  .where('toAccountId', '==', toId)
  .orderBy('date', 'desc')
```

#### Categories
```typescript
// Get active categories
categories
  .where('isActive', '==', true)
  .orderBy('name', 'asc')

// Search by keywords
categories
  .where('keywords', 'array-contains-any', searchTerms)
```

## Performance Considerations

### Index Optimization
- All frequently queried field combinations have composite indexes
- Single-field indexes are automatically created by Firestore
- Array fields have appropriate array-contains indexes

### Query Optimization
- Limit results using `.limit()` for large datasets
- Use pagination for infinite scroll scenarios
- Cache frequently accessed data locally

### Cost Optimization
- Minimize document reads through efficient querying
- Use local caching to reduce Firestore calls
- Implement offline-first architecture with sync

## Migration Strategy

### Phase 1 to Phase 2
1. Deploy new Firestore rules and indexes
2. Update app to handle new collections
3. Migrate existing data to include category references
4. Enable new Phase 2 features gradually

### Data Integrity
- All migrations maintain data consistency
- Rollback procedures available for each migration step
- Comprehensive testing before production deployment

## Monitoring and Maintenance

### Performance Monitoring
- Track query performance using Firebase Performance Monitoring
- Monitor index usage and optimization opportunities
- Set up alerts for unusual query patterns

### Data Maintenance
- Regular cleanup of soft-deleted records
- Archive old data to reduce active dataset size
- Monitor storage usage and costs

## Security Best Practices

### Rule Testing
- Comprehensive unit tests for all security rules
- Regular security audits and penetration testing
- Monitoring for unauthorized access attempts

### Data Protection
- All sensitive data encrypted in transit and at rest
- Regular backups with point-in-time recovery
- Compliance with data protection regulations
